import React from 'react';
import { Link as JssLink } from '@sitecore-jss/sitecore-jss-react';
import { useTranslation } from 'react-i18next';
import { ChevronRightIcon } from '@pmi/catalyst-icons';
import { PATHS, PRODUCTDETAILPAGES_PHRASES } from '@/constants';
import { withClickTracking } from '@pmi/www-shared/analytics';
import { Link } from '@pmi/catalyst-link';
import { cn } from '@pmi/catalyst-utils';
import { InnerItemViewProps, ItemViewProps, SelectedItemViewProps } from './models';

const InnerItemView: React.FC<InnerItemViewProps> = ({ language, selected }) => {
  const { t } = useTranslation();

  const currentViewPhrase = t(PRODUCTDETAILPAGES_PHRASES.CURRENT_VIEW, 'current view');

  return (
    <span>
      {language.name}
      {selected && <> ({currentViewPhrase})</>}
      <i className="absolute mt-1">
        <ChevronRightIcon size="xs" />
      </i>
    </span>
  );
};

const ItemView: React.FC<ItemViewProps> = ({ language }) => {
  return (
    <Link asChild className={cn('!leading-5 text-lg')}>
      <JssLink
        field={{ href: `${PATHS.DCPDP_WITH_SKU}${language.value}` }}
        onClick={withClickTracking<HTMLAnchorElement>(language.name, `${PATHS.DCPDP_WITH_SKU}${language.value}`)}
        editable={false}
        aria-label={language.name}
      >
        <InnerItemView language={language} selected={false} />
      </JssLink>
    </Link>
  );
};

const SelectedItemView: React.FC<SelectedItemViewProps> = ({ language }) => {
  return (
    <Link disabled className={cn('text-lg !leading-5')}>
      <InnerItemView selected language={language} />
    </Link>
  );
};

export { ItemView, SelectedItemView };
