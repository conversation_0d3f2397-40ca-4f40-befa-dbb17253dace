{"name": "spx-headless", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"inner:build:server": "cross-env BUILD_SERVER=true NODE_ENV=development tsx ./scripts/runDevBuild.ts", "inner:build:client": "cross-env BUILD_CLIENT=true NODE_ENV=development tsx ./scripts/runDevBuild.ts", "inner:build:all": "cross-env BUILD_SERVER=true BUILD_CLIENT=true NODE_ENV=development tsx ./scripts/runDevBuild.ts", "inner:build:server:watch": "cross-env USE_WEBPACK_WATCH=true npm run build:server", "inner:build:client:watch": "cross-env USE_WEBPACK_WATCH=true npm run build:client", "inner:build:all:watch": "cross-env USE_WEBPACK_WATCH=true npm run build:all", "inner:devserver:client": "tsx ./scripts/runClientDevServer.ts", "build:server": "run-s inner:build:server", "build:client": "run-s inner:build:client", "build:all": "run-s inner:build:all", "build:server:watch": "run-p inner:build:server:watch", "build:client:watch": "run-p inner:build:client:watch", "build:all:watch": "run-p inner:build:all:watch", "devserver:client": "run-p inner:devserver:client", "lint": "eslint \"./src/**/*.{js,jsx,ts,tsx}\"", "lint:report": "eslint -f json -o output/eslint/eslint_report.json", "prettier": "prettier \"./src/**/*.{js,jsx,tsx,ts}\" ./build/**/*.js --write", "bootstrap:libs": "node ./scripts/runDevBuild.js", "release": "cross-env NODE_ENV=production tsx ./scripts/runBuild4CICD.ts", "test": "jest --silent", "test:coverage": "jest --ci --coverage --silent", "test:update": "jest --updateSnapshot", "test:watch": "jest --watch"}, "author": "", "license": "ISC", "engines": {"node": "^22.5.1", "npm": "^10.8.2", "yarn": "yarn is not supported, please use npm"}, "devDependencies": {"@rspack/cli": "^1.2.2", "@rspack/core": "^1.2.2", "@swc/core": "^1.10.11", "@swc/helpers": "^0.5.12", "@swc/jest": "^0.2.37", "@testing-library/dom": "^10.1.0", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/react": "^18.2.58", "@types/react-dom": "^18.2.25", "@types/react-helmet": "^6.1.11", "@types/react-redux": "^7.1.33", "@types/url-parse": "^1.4.11", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^10.4.19", "classnames": "^2.5.1", "cross-env": "^7.0.3", "css-loader": "^6.10.0", "dotenv": "^16.4.5", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.2", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "jest-sonar-reporter": "^2.0.0", "mini-css-extract-plugin": "^2.8.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.38", "postcss-loader": "^8.1.1", "postcss-preset-env": "^9.5.4", "prettier": "^3.4.2", "style-loader": "^3.3.4", "tailwindcss": "^3.4.3", "ts-jest": "^29.1.3", "ts-node": "^10.9.2", "tsx": "^4.19.2"}, "dependencies": {"@coveo/auth": "^1.11.22", "@coveo/headless": "^3.16.0", "@microsoft/signalr": "^8.0.7", "@pmi/analytics-layer": "^1.0.81", "@pmi/catalyst-accordion": "^0.5.4", "@pmi/catalyst-alert-dialog": "^0.2.3", "@pmi/catalyst-badge": "^0.5.2", "@pmi/catalyst-breadcrumb": "^0.1.5", "@pmi/catalyst-button": "^0.7.3", "@pmi/catalyst-card": "^0.2.2", "@pmi/catalyst-carousel": "^0.1.9", "@pmi/catalyst-dialog": "^0.3.4", "@pmi/catalyst-icons": "^0.7.3", "@pmi/catalyst-link": "^0.2.3", "@pmi/catalyst-popover": "^0.4.6", "@pmi/catalyst-select": "^0.5.10", "@pmi/catalyst-separator": "^0.4.2", "@pmi/catalyst-skeleton": "^0.3.1", "@pmi/catalyst-spinner": "^0.2.3", "@pmi/catalyst-tabs": "^0.3.2", "@pmi/catalyst-text-field": "^0.1.5", "@pmi/catalyst-theme": "^2.0.0", "@pmi/catalyst-tooltip": "^0.4.5", "@pmi/catalyst-typography": "^0.2.2", "@pmi/catalyst-utils": "^0.1.2", "@pmi/catalyst-wordmarks": "^0.7", "@pmi/shared-component-factory": "^9.2", "@pmi/www-shared": "~1.6", "@reduxjs/toolkit": "^2.2.5", "@rtk-query/graphql-request-base-query": "^2.3.1", "@sitecore-jss/sitecore-jss-react": "~22.0", "ahooks": "^3.8.0", "glob": "^10.3.12", "i18next": "^23.11.4", "i18next-fetch-backend": "^6.0.0", "jotai": "^2.9.0", "logrocket": "^9.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-i18next": "^14.1.1", "react-number-format": "^4.3.1", "react-redux": "^9.1.2", "url-parse": "^1.5.10", "uuid": "^10.0.0"}, "jestSonar": {"reportPath": "output/jest/tests", "reportFile": "test-report.xml", "indent": 4}}