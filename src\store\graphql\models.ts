export interface CartLineModelBase {
  displayName: string;
  id: string;
  quantity: number;
  quantityOrdered: number;
  sku: string;
}

export interface BundleOptionModel {
  optionId: number;
  selectedProductType: string;
  selectionId: number;
  selectionName: string;
  selectionSku: string;
}

export interface ShoppingCartLineBase extends CartLineModelBase {
  product: {
    productId: string;
    isMembership: boolean;
  };
  selectedBundleOptions: BundleOptionModel[];
}

export interface CartModelBase {
  cartLines: CartLineModelBase[];
  id: string;
  itemsCount: number;
  itemsQuantity: number;
  membershipCartLine: CartLineModelBase;
}

export interface ShoppingCartBaseData extends CartModelBase {
  cartLines: ShoppingCartLineBase[];
  donationCartLine: ShoppingCartLineBase;
  membershipCartLine: ShoppingCartLineBase;
}

export interface ShoppingCartBaseResponse {
  cart: ShoppingCartBaseData;
}
