import { PATHS } from '@/constants';

export const getCurrentUrl = () => (typeof window === 'undefined' ? null : window.location.href);

export const redirectToPageUrl = (redirectUrl: string) => {
  if (typeof window !== 'undefined') {
    window.location.href = redirectUrl;
  }
};

export const navigateToUrl = (redirectUrl: string) => {
  if (typeof window !== 'undefined') {
    window.location.href = redirectUrl;
  }
};

export const navigateToCartPageWithSku = (sku: string | number, location?: string) => {
  if (location) {
    navigateToUrl(`${PATHS.CART_PAGE_WITH_SKU}${sku}&location=${location}`);
  } else {
    navigateToUrl(`${PATHS.CART_PAGE_WITH_SKU}${sku}`);
  }
};

export const navigateToCartPageWithMembership = (membershipType: string, location?: string) => {
  if (location) {
    navigateToUrl(`${PATHS.CART_PAGE_WITH_MEMBERSHIP}${membershipType}&location=${location}`);
  } else {
    navigateToUrl(`${PATHS.CART_PAGE_WITH_MEMBERSHIP}${membershipType}`);
  }
};

export const navigateToCartPageWithDonation = (amount: string | number) => {
  navigateToUrl(`${PATHS.CART_PAGE_WITH_DONATION}${amount}`);
};

export const navigateToLoginWithRedirect = (redirectUrl: string) => {
  navigateToUrl(`${PATHS.LOGIN_WITH_REDIRECT}${redirectUrl}`);
};

export const navigateToLoginWithRedirectToCurrentPage = () => {
  const redirectUrl = getCurrentUrl();
  navigateToUrl(`${PATHS.LOGIN_WITH_REDIRECT}${redirectUrl}`);
};

export const navigateToLoginWithRedirectToCurrentPageWithQuery = (query: Record<string, string>) => {
  const redirectUrl = new URL(getCurrentUrl());
  Object.entries(query).forEach(([key, value]) => {
    redirectUrl.searchParams.set(key, value);
  });
  navigateToUrl(`${PATHS.LOGIN_WITH_REDIRECT}${redirectUrl}`);
};

export const navigateToLoginWithRedirectToCartPageWithSku = (sku: string | number, location?: string) => {
  if (location) {
    const encodedRedirect = encodeURIComponent(`&location=${location}`);
    navigateToUrl(`${PATHS.LOGIN_WITH_REDIRECT_TO_CART_PAGE_WITH_SKU}${sku}${encodedRedirect}`);
  } else {
    navigateToUrl(`${PATHS.LOGIN_WITH_REDIRECT_TO_CART_PAGE_WITH_SKU}${sku}`);
  }
};

export const navigateToLoginWithMembershipCheckRedirect = (sku: string | number, location?: string) => {
  const encodedContinueUrl = encodeURIComponent(`${PATHS.MEMBERSHIPCHECK_HANDLER}?sku=${sku}&pdpurl=${location}`);
  const redirectToUrl = `${PATHS.LOGIN_WITH_MEMBERSHIP_CHECK_HANDLER_WITH_SKU}${encodedContinueUrl}`;
  navigateToUrl(redirectToUrl);
};

export const navigateToLoginWithRedirectToCartPageWithDonation = (amount: string | number) => {
  navigateToUrl(`${PATHS.LOGIN_WITH_REDIRECT_TO_CART_PAGE_WITH_DONATION}${amount}`);
};
