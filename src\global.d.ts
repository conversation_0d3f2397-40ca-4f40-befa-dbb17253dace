declare let SERVER_VARS: {
  NODE_ENV: 'development' | 'production';
};

namespace NodeJS {
  interface ProcessEnv {
    SHARED_API_BASE_URL: string;
    SPX_API_BASE_URL: string;
    PMI_GRAPHQL_URL: string;
    SITECORE_URL: string;
    SITECORE_API_KEY: string;
    SITECORE_SITE_NAME: string;
    SITECORE_CONFIG_NAME: string;
    SSR_ERROR_CODE: string;
  }
}

declare let SIGNALR_VARS: {
  SINGALR_URL: string;
};
