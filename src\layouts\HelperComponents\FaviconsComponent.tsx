import React from 'react';

export const FaviconsComponent: React.FC = () => (
  <>
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/-/media/pmi/icons/favicon/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/-/media/pmi/icons/favicon/favicon-16x16.png" />
    <link rel="manifest" href="/-/media/pmi/icons/favicon/site.webmanifest" />
    <link rel="mask-icon" href="/-/media/pmi/icons/favicon/safari-pinned-tab.svg" color="#5bbad5" />
    <link rel="shortcut icon" href="/-/media/pmi/icons/favicon/favicon.ico" />
  </>
);
