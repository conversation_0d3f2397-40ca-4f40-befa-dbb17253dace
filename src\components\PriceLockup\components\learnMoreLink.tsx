import { TextlinkChevronIcon } from '@pmi/catalyst-icons';
import { Link } from '@pmi/catalyst-link';

export const LearnMoreLink: React.FC<{ text?: string; redirectLink?: string }> = ({ text, redirectLink }) => {
  if (!text || !redirectLink) return null;
  return (
    <div className="col-span-4 pt-[--scale-16] lg:pt-[--scale-12]">
      <Link href={redirectLink}>
        <span className="text-body-xs lg:text-body-md font-medium">{text}</span>
        <TextlinkChevronIcon size="full" className="size-[--scale-16] lg:size-[--scale-20]" />
      </Link>
    </div>
  );
};
