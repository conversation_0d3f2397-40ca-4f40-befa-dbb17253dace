import { useGetActiveMembershipsQuery } from '@pmi/www-shared/store';
import { CommerceProductBase } from '@/models';
import { getProductPricing, isElearningProduct } from '@/utils/Product/product';
import { UserStatuses, useUserStatus } from '@pmi/www-shared/hooks';

export const useIsFreeProduct = (fields: CommerceProductBase) => {
  const { memberPrice, regularPrice } = getProductPricing(fields);
  const isFreeRegularPrice = regularPrice === 0;
  const isFreeMemberPrice = memberPrice === 0;
  const userStatus = useUserStatus();
  const isAuthenticated = userStatus === UserStatuses.LoggedIn;
  const { data: activeMemberships } = useGetActiveMembershipsQuery(null, { skip: !isAuthenticated });
  const isMember = !!activeMemberships?.hasActiveMembership;

  const isFreeProduct = (isMember && isFreeMemberPrice) || isFreeRegularPrice;
  const isFreeProductActivationEligible = isFreeProduct && isElearningProduct(fields);

  return { isFreeProduct, isFreeProductActivationEligible };
};
