import React from 'react';
import { withEditorChromes } from '@sitecore-jss/sitecore-jss-react';
import { Link } from '@pmi/catalyst-link';
import { TextlinkChevronIcon } from '@pmi/catalyst-icons';
import { useIsExperienceEditor } from '@/hooks';

import { LinkCtaProps } from './models';
import { MissingDataSource } from '../common';

const Component: React.FC<LinkCtaProps> = ({ fields }) => {
  const isExperienceEditor = useIsExperienceEditor();

  const { Link: link } = fields ?? {};
  if (!link?.value?.href || !link?.value?.text) {
    return isExperienceEditor ? <MissingDataSource /> : null;
  }

  return (
    <div className="pt-[--scale-12]">
      <Link href={link.value.href}>
        {link.value.text}
        <TextlinkChevronIcon size="md" />
      </Link>
    </div>
  );
};

export default withEditorChromes(Component);
