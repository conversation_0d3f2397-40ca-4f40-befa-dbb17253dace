import { render } from '@testing-library/react';
import { useIsExperienceEditor } from '@/hooks';
import ChapterInformation from './detailView';

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      return key;
    },
  }),
}));

jest.mock('@/hooks', () => ({
  useIsExperienceEditor: jest.fn(),
}));

jest.mock('@sitecore-jss/sitecore-jss-react', () => ({
  useSitecoreContext: jest.fn(),
  withEditorChromes: (component: any) => component,
  Text: () => jest.fn(),
}));

jest.mock('@/hocs', () => ({
  withProductFields: (component: any) => component,
}));

describe('ChapterCharterInformation detailView tests', () => {
  it('should be rendered empty', () => {
    (useIsExperienceEditor as jest.Mock).mockReturnValue(false);
    const props = { fields: {} };
    const { container } = render(<ChapterInformation {...props} />);
    expect(container.firstChild).toBeNull();
  });

  it('should MissingDataSource be rendered ', () => {
    (useIsExperienceEditor as jest.Mock).mockReturnValue(true);
    const expectedValue = 'This component is missing a data source';
    const props = { fields: {} };
    const { container } = render(<ChapterInformation {...props} />);

    expect(container.firstChild).toContainHTML(expectedValue);
  });

  it('should be rendered', () => {
    (useIsExperienceEditor as jest.Mock).mockReturnValue(true);
    const date = new Date();
    const chapterStatus = 'Chartered';
    const props = {
      fields: {
        region: {
          fields: {
            Label: {
              value: 'Region',
            },
          },
        },
        charter_status: {
          fields: {
            Label: {
              value: chapterStatus,
            },
          },
        },
        charter_year: {
          value: date,
        },
      },
    };

    const { getByText } = render(<ChapterInformation {...props} />);
    const expectedText = `${chapterStatus} since ${date.getFullYear()}`;
    const component = getByText(expectedText);

    expect(component).toBeVisible();
  });
});
