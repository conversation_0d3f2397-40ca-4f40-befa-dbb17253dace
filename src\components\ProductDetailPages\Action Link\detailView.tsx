import React from 'react';
import { But<PERSON> } from '@pmi/catalyst-button';
import { ExternalLinkIcon } from '@pmi/catalyst-icons';
import { PdpActionLinkProps } from './models';

export const PdpActionLink: React.FC<PdpActionLinkProps> = ({ fields }) => {
  return (
    <div className="flex justify-center lg:justify-start">
      <Button
        asChild
        className="theme-pmi-dark inline-flex mt-6 lg:mt-[--scale-112] h-[--scale-40] font-medium px-[--scale-16] hover:text-[--text-primary]"
      >
        <a href={fields?.Link?.value?.href}>
          {fields?.Link?.value?.text || 'Visit the PMIEF Website'}
          <ExternalLinkIcon color="inverted" size="sm" className="transition group-hover:text-[--text-primary]" />
        </a>
      </Button>
    </div>
  );
};
