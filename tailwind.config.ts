import { tailwindAccordionPreset } from '@pmi/catalyst-accordion';
import { tailwindTypographyPreset } from '@pmi/catalyst-typography';
import { tailwindDialogPreset } from '@pmi/catalyst-dialog';
import { tailwindThemePreset } from '@pmi/catalyst-theme';
import { tailwindWwwSharedPresets } from '@pmi/www-shared/tailwind';
import type { PluginAPI } from 'tailwindcss/types/config';

export default {
  content: [
    './src/**/*.{ts,tsx,js,jsx}',
    '!./src/**/*.test.{ts,tsx,js,jsx}',
    './node_modules/@pmi/catalyst-*/**/*.{ts,tsx,js,jsx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        display: ['Aeonik', 'Arial', 'sans-serif'],
      },
      screens: {
        mobile: { max: '1023.9px' },
      },
    },
  },
  presets: [
    tailwindAccordionPreset,
    tailwindTypographyPreset,
    tailwindDialogPreset,
    tailwindThemePreset,
    tailwindWwwSharedPresets,
  ],
  plugins: [
    function ({ addUtilities }: PluginAPI) {
      const customScrollbars = {
        '.mobile-scrollbar': {
          '&::-webkit-scrollbar': {
            width: 'var(--scale-10)',
            height: 'var(--scale-10)',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'var(--fill-neutral-darker)',
            borderRadius: 'var(--rounded-xs)',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'var(--fill-neutral-softest)',
          },
        },
      };
      const customeWrap = {
        '.overflow-wrap-anywhere': {
          overflowWrap: 'anywhere',
        },
      };

      addUtilities([customScrollbars, customeWrap]);
    },
  ],
};
