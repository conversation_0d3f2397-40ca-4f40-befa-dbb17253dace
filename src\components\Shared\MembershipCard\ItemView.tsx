import React from 'react';
import { Card } from '@pmi/catalyst-card';
import { cn } from '@pmi/catalyst-utils';
import { Badge, ColorProps, VariantProps } from '@pmi/catalyst-badge';
import { IconByName, WordmarkByName } from '@/components/common';
import { LinkField, Text } from '@sitecore-jss/sitecore-jss-react';
import { DefaultButton } from '@pmi/www-shared/components';
import { useClickPropagation } from '@pmi/www-shared/hooks';
import { usePriceLookupContext } from '@/components/PriceLockup/context';
import { usePriceLockupTexts } from '@/components/PriceLockup/usePriceLockupTexts';
import { PATHS } from '@/constants';
import { getDisplayPrice } from '@/utils';
import BadgeTextView from '../../ProductDetailPages/ProductBadges/badgeTextView';
import { HighlightTextColorMap, MembershipCardProps } from './models';

const Component: React.FC<MembershipCardProps> = ({ product, membershipTextHighlightColor }) => {
  const buttonContainerRef = React.useRef<HTMLDivElement>(null);
  const [btnRef, onCardClick] = useClickPropagation();
  const { addToCartButtonText, regularPriceText } = usePriceLockupTexts();
  const { isAuthenticated } = usePriceLookupContext();

  const generateAddToCartRedirectUrl = () => {
    if (!isAuthenticated) {
      if (product.regularPrice > 0 && product.memberPrice === 0) {
        const encodedContinueUrl = encodeURIComponent(
          `${PATHS.MEMBERSHIPCHECK_HANDLER}?sku=${product.sku}&pdpurl=${window?.location?.pathname}`,
        );
        return `${PATHS.LOGIN_WITH_MEMBERSHIP_CHECK_HANDLER_WITH_SKU}${encodedContinueUrl}`;
      }
      return `${PATHS.LOGIN_WITH_REDIRECT_TO_CART_PAGE_WITH_SKU}${product.sku}`;
    }
    if (window?.location?.pathname) {
      return `${PATHS.CART_PAGE_WITH_SKU}${product.sku}&location=${window?.location?.pathname}`;
    }
    return `${PATHS.CART_PAGE_WITH_SKU}${product.sku}`;
  };

  const jssAddToCartLink: LinkField = {
    value: {
      href: generateAddToCartRedirectUrl(),
      url: generateAddToCartRedirectUrl(),
      text: addToCartButtonText,
      title: addToCartButtonText,
      target: '',
      linktype: 'external',
    },
  };

  const styles = cn(
    'w-[312px]',
    'lg:w-[372px]',
    'min-h-[480px]',
    'lg:min-h-[552px]',
    'flex flex-col',
    'justify-between',
    'text-[--text-primary]',
    'px-[--scale-24]',
    'py-[--scale-32]',
    'gap-[--scale-16]',
    'relative',
    'group',
    'overflow-hidden',
    'h-full',
    'font-display',
    product.useDarkThemeForHero ? 'theme-pmi-dark' : 'theme-pmi-light',
  );

  const mappedPriceBackgroundColor =
    HighlightTextColorMap[membershipTextHighlightColor as keyof typeof HighlightTextColorMap];

  const pricesStyle = cn(
    'flex flex-col',
    'rounded-lg',
    'pt-[--scale-8]',
    'pb-[--scale-8]',
    'pl-[--scale-12]',
    'pr-[--scale-12]',
    mappedPriceBackgroundColor?.length > 0 ? mappedPriceBackgroundColor : HighlightTextColorMap.Grey,
  );

  const backgroundImageUrl = product.heroBackgroundImageMobile?.value?.src;
  return (
    <Card className={styles} variant="outline" onClick={onCardClick}>
      <div className="flex z-10 justify-between">
        <div className="flex gap-[--scale-8] flex-wrap">
          {product.badges?.map((badge) => {
            const text = badge?.value;
            const color = (badge?.color as ColorProps) || 'off-black';
            const variant = (badge?.variant as VariantProps) || 'outline';
            const badgeIcon = badge?.badgeIcon;
            return (
              <Badge key={text} color={color} variant={variant} className="h-[--scale-24]">
                {badgeIcon && <IconByName name={badgeIcon} size="xs" color="original" className="mr-1" />}
                <BadgeTextView icon={badgeIcon} text={text} />
              </Badge>
            );
          })}
        </div>
        {product.wordmark && (
          <div className="h-[--scale-48]">
            <WordmarkByName
              name={product.wordmark}
              color="original"
              size="full"
              className="rotate-[270deg] origin-bottom-right -translate-y-full"
            />
          </div>
        )}
      </div>
      <div
        className="z-10 flex pt-[var(--scale-16,16px)] flex-col justify-end items-start gap-[var(--scale-24,24px)] self-stretch"
        ref={buttonContainerRef}
      >
        <h2 className="mb-[--scale-16] font-medium text-header-sm lg:text-header-md">
          <Text field={{ value: product.title }} encode={false} />
        </h2>
        <p className="font-normal text-body-sm lg:text-body-md text-[--text-secondary]">{product.abstract}</p>
        <div className="grid grid-cols-2 gap-[--scale-32]">
          <div
            className={`flex flex-col pt-[--scale-8] pb-[--scale-8] ${product.memberPrice == null ? pricesStyle : ''}`}
          >
            <p className="text-eyebrow font-medium">{regularPriceText}</p>
            <p className="text-header-2xs font-medium">
              {product.currencySymbol}
              {getDisplayPrice(product.regularPrice)}
            </p>
          </div>
        </div>
        <div className="col-span-4 lg:col-start-5 lg:col-span-2 flex font-medium">
          <DefaultButton
            ref={btnRef}
            variant="solid"
            className="w-full lg:w-max mt-0"
            jssLinkField={jssAddToCartLink}
            isInClickableArea
          />
        </div>
      </div>
      {backgroundImageUrl && (
        <picture className="absolute top-0 right-0 left-0 group-hover:scale-[1.05] transition">
          <img src={backgroundImageUrl} className="object-cover w-full" alt="Card Background" role="presentation" />
        </picture>
      )}
    </Card>
  );
};

export default Component;
