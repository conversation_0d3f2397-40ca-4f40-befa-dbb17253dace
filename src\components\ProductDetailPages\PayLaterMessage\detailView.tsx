import React, { useMemo } from 'react';
import { withEditorChromes, RichText } from '@sitecore-jss/sitecore-jss-react';
import { useGetActiveMembershipsQuery, useGetCoursesQuery, useGetUserQuery } from '@pmi/www-shared/store';
import { Skeleton } from '@pmi/catalyst-skeleton';
import { useIsExperienceEditor, useIsFreeProduct, useRouteFields } from '@/hooks';
import { MissingDataSource } from '@/components/common';
import { isElearningProduct, isMembershipProduct } from '@/utils';
import { CommerceProductBase, Course } from '@/models';
import { PayLaterMessageProps } from './models';

const Component: React.FC<PayLaterMessageProps> = ({ fields }) => {
  const isExperienceEditor = useIsExperienceEditor();
  const routeFields = useRouteFields<CommerceProductBase>();
  const { isLoading: userDataLoading, data: user } = useGetUserQuery();
  const { isLoading: coursesLoading, data: userCourses } = useGetCoursesQuery(null, { skip: !user });
  const { data: activeMemberships, isLoading: activeMembershipLoading } = useGetActiveMembershipsQuery(undefined, {
    skip: !user,
  });
  const isMember = Boolean(activeMemberships?.hasActiveMembership);
  const canRenew =
    activeMemberships?.membership?.canRenew && activeMemberships?.membership?.autoRenewStatus !== 'OptIn';
  const isStudentMember = Boolean(activeMemberships?.membership?.type?.includes('student'));
  const isActiveMembershipNotInRenewal = (isMember || isStudentMember) && !canRenew;
  const isFreeProduct = useIsFreeProduct(routeFields);
  const isFree = isFreeProduct?.isFreeProduct;
  const { MessageLight: messageLight, MessageDark: messageDark } = fields;
  const { ExternalID: sku, UseDarkThemeForHero: isDarkTheme } = routeFields;
  const message = isDarkTheme?.value ? messageDark : messageLight;
  const isDataLoading = userDataLoading || coursesLoading || activeMembershipLoading;

  const purchasedCourse = useMemo(() => {
    return (userCourses as Course[])?.find((c) => c?.courseId === sku);
  }, [userCourses, sku]);

  if (isDataLoading) {
    return <Skeleton className="block w-full h-[--scale-24]">Loading</Skeleton>;
  }

  if (!message?.value) {
    return isExperienceEditor ? <MissingDataSource /> : null;
  }

  if (
    isFree ||
    (isElearningProduct(routeFields) && purchasedCourse) ||
    (isMembershipProduct(routeFields) && isActiveMembershipNotInRenewal)
  ) {
    return null;
  }

  return (
    <div className="w-full my-3">
      <RichText field={message} encode={false} />
    </div>
  );
};

export default withEditorChromes(Component);
