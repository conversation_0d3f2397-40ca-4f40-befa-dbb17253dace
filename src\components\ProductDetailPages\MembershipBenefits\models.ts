import { RichTextField, TextField } from '@sitecore-jss/sitecore-jss-react';
import * as Jss from '@/models/Jss';

export interface MembershipBenefitsProps extends Jss.Rendering<MembershipBenefitsDataSource> {}

export interface MembershipBenefitsDataSource extends Jss.BaseDataSourceItem {
  Items: MembershipBenefitItem[];
}

export interface MembershipBenefitItem {
  fields: MembershipBenefit;
}

export interface MembershipBenefit {
  id: string;
  Title: TextField;
  Content: RichTextField;
}

export interface TabbedContentProps {
  content: MembershipBenefit[];
  selected: string;
  setSelected: (value: string) => void;
}
