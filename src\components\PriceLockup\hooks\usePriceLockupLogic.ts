import { usePriceLookupContext } from '@/components/PriceLockup/context';
import { ButtonInfo, MembershipInfo, ProductInfo } from '@/components/PriceLockup/models';
import { usePriceLockupTexts } from '@/components/PriceLockup/usePriceLockupTexts';
import { useProductContext } from '@/containers';
import { useRouteFields, useSpxHeadlessSettings } from '@/hooks';
import { CommerceProductBase } from '@/models';
import {
  getCountryPriceByCode,
  getMembershipType,
  getProductPricing,
  hasChapterOptionInMembershipProduct,
} from '@/utils';
import { isStudentMembershipProduct } from '@/utils/Product/product';
import { useGetActiveMembershipsQuery, useGetStoreQuery } from '@pmi/www-shared/store';
import { useCallback, useMemo } from 'react';

// this needs to be refactored to have multiple smaller hooks instead of one giant hook.
export const usePriceLockupLogic = () => {
  const { fields, isAuthenticated } = usePriceLookupContext();
  const { product: contextProduct } = useProductContext();
  let product = useRouteFields<CommerceProductBase>();
  if (contextProduct) {
    product = contextProduct;
  }
  const { data: userStoreData, isLoading: isUserStoreDataLoading } = useGetStoreQuery();
  const { data: activeMemberships, isLoading: activeMembershipLoading } = useGetActiveMembershipsQuery(undefined, {
    skip: !isAuthenticated,
  });
  const { addToCartButtonText } = usePriceLockupTexts();
  const settings = useSpxHeadlessSettings();
  const hasChapterBundleOption = hasChapterOptionInMembershipProduct(product);

  const userCountryCode = userStoreData?.countryCode3;
  const isSingleMembershipEnabled = userStoreData?.singleMembershipEnabled;

  const isIndiaStore = useMemo(() => userCountryCode === 'IND', [userCountryCode]);

  const isProductSkuInActiveChapterMemberships = () => {
    return activeMemberships?.chapterMemberships.some(
      (chapterMembership) => chapterMembership.sku === product?.ExternalID?.value,
    );
  };

  const showCurrencyCode = (code: string) => {
    return ['USD'].includes(code?.toUpperCase());
  };

  const membershipInfo: MembershipInfo = useMemo(
    () => ({
      isMember: Boolean(activeMemberships?.hasActiveMembership),
      canRenew: activeMemberships?.membership?.canRenew && activeMemberships?.membership?.autoRenewStatus !== 'OptIn',
      autoRenewStatus: activeMemberships?.membership?.autoRenewStatus ?? '',
      isStudentMember: Boolean(activeMemberships?.membership?.type?.includes('student')),
      expirationDate: new Date(activeMemberships?.membership?.endDate).toLocaleDateString() ?? '',
      currentChapter: activeMemberships?.chapterMemberships.find(
        (chapterMembership) => chapterMembership.sku === product?.ExternalID?.value,
      ),
    }),
    [activeMemberships, product?.ExternalID?.value],
  );

  const productInfo: ProductInfo = useMemo(() => {
    const {
      regularPrice,
      currencyCode,
      currencySymbol,
      renewalPrice: regularRenewalPrice,
    } = getProductPricing(product);
    const { price: countryPrice = 0, renewal_price: countryRenewalPrice = 0 } =
      getCountryPriceByCode(product, userCountryCode) || {};

    let priceToShow: number;

    if (isSingleMembershipEnabled) {
      if (membershipInfo.canRenew) {
        if (countryRenewalPrice > 0) {
          priceToShow = countryRenewalPrice;
        } else if (countryPrice > 0) {
          priceToShow = countryPrice;
        } else if (regularRenewalPrice > 0) {
          priceToShow = regularRenewalPrice;
        } else {
          priceToShow = regularPrice;
        }
      } else {
        priceToShow = countryPrice > 0 ? countryPrice : regularPrice;
      }
    } else {
      priceToShow = regularPrice;
    }

    return {
      productMembershipType: getMembershipType(product),
      currencyCode: showCurrencyCode(currencyCode) ? currencyCode : null,
      currencySymbol,
      countryPrice,
      regularPrice,
      priceToShow,
      hasChapterBundleOption,
      productName: product?.Name?.value as string,
      productSku: product?.ExternalID?.value as string,
      isDeleted: product?.IsDeleted?.value,
      isSingleMembershipEnabledCountry: isSingleMembershipEnabled,
      isStudentMembershipProduct: isStudentMembershipProduct(product),
    };
  }, [product, userCountryCode, isSingleMembershipEnabled, hasChapterBundleOption, membershipInfo.canRenew]);

  const skuMappingDictionary = useMemo(
    () =>
      settings?.MembershipSKUMapping?.split('&').map((value) => {
        const [key, skus] = value.split('=');
        return {
          key,
          skus: new Set(skus.split('%7C')),
        };
      }),
    [settings?.MembershipSKUMapping],
  );

  const membershipTypeRedirect = useMemo(
    () => skuMappingDictionary?.find((mapping) => mapping?.skus?.has(product?.ExternalID?.value as string))?.key,
    [skuMappingDictionary, product?.ExternalID?.value],
  );

  const getMembershipProductButtonText = useCallback(() => {
    if ((membershipInfo.isMember || membershipInfo.isStudentMember) && !membershipInfo.canRenew) {
      return fields.viewInAccountButtonText?.value;
    }
    if ((!membershipInfo.isMember || !membershipInfo.isStudentMember) && membershipInfo.canRenew) {
      return fields.renewNowButtonText?.value;
    }
    return addToCartButtonText;
  }, [
    membershipInfo.isMember,
    membershipInfo.isStudentMember,
    membershipInfo.canRenew,
    addToCartButtonText,
    fields.viewInAccountButtonText?.value,
    fields.renewNowButtonText?.value,
  ]);

  const buttonInfo: ButtonInfo = useMemo(
    () => ({
      text: getMembershipProductButtonText(),
      isDisabled: product?.IsDeleted?.value === true && !membershipInfo.canRenew && !membershipInfo.isMember,
    }),
    [getMembershipProductButtonText, product?.IsDeleted?.value, membershipInfo.canRenew, membershipInfo.isMember],
  );

  return {
    membershipInfo,
    productInfo,
    buttonInfo,
    activeMembershipLoading,
    fields,
    isLoading: isUserStoreDataLoading || activeMembershipLoading,
    settings,
    membershipTypeRedirect,
    product,
    isIndiaStore,
    isProductSkuInActiveChapterMemberships,
  };
};
