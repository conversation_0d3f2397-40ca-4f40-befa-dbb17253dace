import React from 'react';
import { cn } from '@pmi/catalyst-utils';
import { getDisplayPrice } from '@/utils/Product/product';
import { stringIsNullOrEmpty } from '@/utils';
import { PriceDisplayProps } from '../models';

const PriceDisplay: React.FC<PriceDisplayProps> = ({
  title,
  price,
  currencyCode,
  currencySymbol,
  isBold,
  isSticky,
  priceNotification,
}) => {
  const displayPrice = getDisplayPrice(price);
  return (
    <div className="col-span-2 text-[--text-primary]">
      <h2 className={cn('text-body-md font-normal', { 'font-bold': isBold })}>{title}</h2>
      {priceNotification && (
        <div className={cn('text-body-sm line-clamp-1 leading-4', { hidden: isSticky })}>{priceNotification}</div>
      )}
      <span className={cn('text-body-md font-normal', { 'font-bold': isBold })}>
        {!stringIsNullOrEmpty(currencyCode) && `${currencyCode} `}
        {currencySymbol}
        {displayPrice}
      </span>
    </div>
  );
};

export default PriceDisplay;
