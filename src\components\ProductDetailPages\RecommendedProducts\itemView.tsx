import React from 'react';
import { <PERSON><PERSON>ield, Text } from '@sitecore-jss/sitecore-jss-react';
import { Card } from '@pmi/catalyst-card';
import { Badge, ColorProps, VariantProps } from '@pmi/catalyst-badge';
import { cn } from '@pmi/catalyst-utils';
import { linkClickTracking } from '@pmi/www-shared/analytics';
import { IconByName, WordmarkByName } from '@/components/common';
import { useClickPropagation } from '@pmi/www-shared/hooks';
import { DefaultButton } from '@pmi/www-shared/components';
import { HighlightTextColorMap } from '@/components/Shared/ProductCard/models';
import { usePriceLockupTexts } from '@/components/PriceLockup/usePriceLockupTexts';
import { usePriceLookupContext } from '@/components/PriceLockup/context';
import { PATHS } from '@/constants';
import BadgeTextView from '../ProductBadges/badgeTextView';
import { RecommendedProductCardProps } from './models';

const Component: React.FC<RecommendedProductCardProps> = ({ product, buttonText }) => {
  const backgroundImageUrl = product?.heroBackgroundImageMobile?.value?.src;
  const buttonContainerRef = React.useRef<HTMLDivElement>(null);
  const [btnRef, onCardClick] = useClickPropagation();
  const altText = product?.recommendedAltText || `${buttonText} about ${product?.parsedTitle}`;
  const jssProductLink: LinkField = {
    value: {
      href: product?.productUrl,
      url: product?.productUrl,
      text: buttonText,
      title: altText,
      target: '',
      linktype: 'external',
    },
  };
  const { membershipPriceText, addToCartButtonText, fullPriceText } = usePriceLockupTexts();

  const handleClick = () => {
    linkClickTracking(`${product?.parsedTitle}-learnmore`, buttonContainerRef.current, product?.productUrl);
  };

  const { isAuthenticated } = usePriceLookupContext();

  const generateAddToCartRedirectUrl = () => {
    if (!isAuthenticated) {
      if (product.regularPrice > 0 && product.memberPrice === 0) {
        const encodedContinueUrl = encodeURIComponent(
          `${PATHS.MEMBERSHIPCHECK_HANDLER}?sku=${product.sku}&pdpurl=${window?.location?.pathname}`,
        );
        return `${PATHS.LOGIN_WITH_MEMBERSHIP_CHECK_HANDLER_WITH_SKU}${encodedContinueUrl}`;
      }
      return `${PATHS.LOGIN_WITH_REDIRECT_TO_CART_PAGE_WITH_SKU}${product.sku}`;
    }
    if (window?.location?.pathname) {
      return `${PATHS.CART_PAGE_WITH_SKU}${product.sku}&location=${window?.location?.pathname}`;
    }
    return `${PATHS.CART_PAGE_WITH_SKU}${product.sku}`;
  };

  const jssAddToCartLink: LinkField = {
    value: {
      href: generateAddToCartRedirectUrl(),
      url: generateAddToCartRedirectUrl(),
      text: addToCartButtonText,
      title: product?.recommendedAltText || addToCartButtonText,
      target: '',
      linktype: 'external',
    },
  };

  const styles = cn(
    'mobile:max-w-[312px] lg:max-w-[370px] mobile:min-h-[480px] lg:min-h-[552px]',
    'flex flex-col',
    'justify-between',
    'text-[--text-primary]',
    'px-[--scale-24]',
    'py-[--scale-32]',
    'gap-[--scale-16]',
    'relative',
    'group',
    'overflow-hidden',
    'h-full',
    product?.useDarkThemeForHero ? 'theme-pmi-dark' : 'theme-pmi-light',
  );

  const mappedPriceBackgroundColor =
    HighlightTextColorMap[product.membershipTextHighlightColor as keyof typeof HighlightTextColorMap];

  const pricesStyle = cn(
    'flex flex-col',
    'rounded-lg',
    'pt-[--scale-8]',
    'pb-[--scale-8]',
    'pl-[--scale-12]',
    'pr-[--scale-12]',
    mappedPriceBackgroundColor?.length > 0 ? mappedPriceBackgroundColor : HighlightTextColorMap.Grey,
  );

  return (
    <Card className={styles} variant="outline" onClick={onCardClick}>
      <div className="flex z-10 justify-between">
        <div className="flex gap-[--scale-8] flex-wrap">
          {product?.badges?.map((badge) => {
            const text = badge?.value;
            const color = (badge?.color as ColorProps) || 'off-black';
            const variant = (badge?.variant as VariantProps) || 'outline';
            const badgeIcon = badge?.badgeIcon;
            return (
              <Badge key={text} color={color} variant={variant} className="h-[--scale-24]">
                {badgeIcon && <IconByName name={badgeIcon} size="xs" color="original" className="mr-1" />}
                <BadgeTextView icon={badgeIcon} text={text} />
              </Badge>
            );
          })}
        </div>
        {product?.wordmark && (
          <div className="h-[--scale-48]">
            <WordmarkByName
              name={product?.wordmark}
              color="original"
              size="full"
              className="rotate-[270deg] origin-bottom-right -translate-y-full"
            />
          </div>
        )}
      </div>
      <div className="z-10" ref={buttonContainerRef}>
        <h2 className="mb-[--scale-16] font-medium text-header-sm lg:text-header-md">
          <Text field={{ value: product?.title }} encode={false} />
        </h2>
        <p className="mb-[--scale-24] font-normal text-body-sm lg:text-body-md text-[--text-secondary]">
          {product?.abstract}
        </p>
        {product?.showPrices && (
          <div className="grid grid-cols-2 gap-[--scale-32] mb-[--scale-24]">
            {product.memberPrice != null && (
              <div className={pricesStyle}>
                <p className="text-eyebrow font-medium">{membershipPriceText}</p>
                <p className="text-header-2xs font-medium">
                  {product.currencySymbol}
                  {product.memberPrice}
                </p>
              </div>
            )}
            <div
              className={`flex flex-col pt-[--scale-8] pb-[--scale-8] ${product.memberPrice == null ? pricesStyle : ''}`}
            >
              <p className="text-eyebrow font-medium">{fullPriceText}</p>
              <p className="text-header-2xs font-medium">
                {product.currencySymbol}
                {product.regularPrice}
              </p>
            </div>
          </div>
        )}
        {!product?.showCTA && (
          <DefaultButton
            ref={btnRef}
            variant="solid"
            className="w-max h-[--scale-48] px-[--scale-24] font-medium"
            jssLinkField={jssProductLink}
            onClick={handleClick}
            isInClickableArea
          />
        )}
        {product?.showCTA && (
          <DefaultButton
            ref={btnRef}
            variant="solid"
            className="w-max h-[--scale-48] px-[--scale-24] font-medium"
            jssLinkField={jssAddToCartLink}
            isInClickableArea
          />
        )}
      </div>
      {backgroundImageUrl && (
        <picture className="absolute top-0 right-0 group-hover:scale-[1.05] transition">
          <img src={backgroundImageUrl} className="object-cover h-full" alt="Card Background" role="presentation" />
        </picture>
      )}
    </Card>
  );
};

export default Component;
