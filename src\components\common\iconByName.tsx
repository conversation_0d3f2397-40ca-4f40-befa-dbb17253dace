import { MembershipIcon } from '@pmi/catalyst-icons';

export interface IconByNameProps {
  name: string;
  className?: string;
  size: 'full' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | null | undefined;
  color:
    | 'accent'
    | 'inherit'
    | 'original'
    | 'primary'
    | 'secondary'
    | 'info'
    | 'danger'
    | 'warning'
    | 'success'
    | 'white'
    | 'inverted'
    | 'neutral-soft'
    | 'neutral-dark'
    | null
    | undefined;
}

export const IconByName: React.FC<IconByNameProps> = (props) => {
  switch (props.name) {
    case 'MembershipIcon':
      return <MembershipIcon size={props.size} color={props.color} className={props.className} />;
    default:
      return null;
  }
};
