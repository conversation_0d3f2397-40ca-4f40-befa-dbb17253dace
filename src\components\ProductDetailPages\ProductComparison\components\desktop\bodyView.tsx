import React from 'react';
import { CircleCheckFilledIcon } from '@pmi/catalyst-icons';
import { cn } from '@pmi/catalyst-utils';
import { BodyRowViewProps, BodyRow, BodyColumnValue } from '../../models';

export const BodyView: React.FC<BodyRowViewProps> = ({ rows, highlightedColumn }) => {
  if (!rows || rows.length === 0) {
    return null;
  }

  return (
    <>
      {rows.map((bodyRow: BodyRow) => (
        <tr key={bodyRow.key} className="odd:bg-[--surface-secondary] text-[--text-primary]">
          <td className="text-left font-display font-normal pr-4 bg-[--surface-primary]">
            <div className="text-body-md">{bodyRow.title}</div>
            {bodyRow.description && <div className="text-base text-[--text-secondary]">{bodyRow.description}</div>}
          </td>
          {bodyRow.columnValues.map((column: BodyColumnValue, columnIndex: number) => {
            const shouldHighlight = columnIndex === highlightedColumn;
            const tdClass = cn('p-0', {
              '[tr:not(:last-child)_&]:border-x-2 border-[--colors-off-black-dark] relative': shouldHighlight,
            });
            const divClass = cn('py-6 flex justify-center w-full text-header-xs font-medium', {
              '[tr:last-child_td_&]:border-x-2 [tr:last-child_td_&]:border-b-2 [tr:last-child_td_&]:rounded-b-3xl border-[--colors-off-black-dark] empty:absolute h-full left-0 top-0':
                shouldHighlight,
            });
            return (
              <td key={column.key} className={tdClass}>
                <div className={divClass}>
                  {typeof column.value === 'boolean'
                    ? column.value && <CircleCheckFilledIcon size="lg" />
                    : column.value}
                </div>
              </td>
            );
          })}
        </tr>
      ))}
    </>
  );
};
