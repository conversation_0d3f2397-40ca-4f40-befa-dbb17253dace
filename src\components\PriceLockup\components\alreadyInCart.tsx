import { AttentionIcon } from '@pmi/catalyst-icons';
import { useTranslation } from 'react-i18next';

interface Props {
  display?: boolean;
}

export const AlreadyInCart: React.FC<Props> = ({ display = false }) => {
  const { t } = useTranslation();
  if (display === false) return null;
  return (
    <div className="mt-2 text-red-500 flex whitespace-nowrap">
      <span className="mr-1 justify-start items-center gap-2 inline-flex">
        <AttentionIcon size="sm" />
        {t('ProductDetailPages.FieldLabels.PriceLockup.AlreadyInCart', 'Already in cart')}
      </span>
    </div>
  );
};
