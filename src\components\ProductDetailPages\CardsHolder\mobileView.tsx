import React from 'react';
import {
  CarouselRoot,
  CarouselItem,
  CarouselContent,
  CarouselNext,
  CarouselPrevious,
  CarouselControls,
  CarouselAnnounce,
  CarouselPagination,
  CarouselPaginationDot,
} from '@pmi/catalyst-carousel';
import { ArrowLeftIcon, ArrowRightIcon } from '@pmi/catalyst-icons';
import { PdpCard } from '../PdpCard';
import { CardsHolderProps } from './models';

export const PdpCardsHolderMobileView: React.FC<CardsHolderProps> = ({ fields }) => {
  const shouldShowCarouselControls = fields?.Cards?.length > 1;

  return (
    <div className="grid grid-cols-4">
      <h5 className="col-span-4 font-display font-medium text-header-md pb-[--scale-24]">{fields?.Title?.value}</h5>
      {fields?.SubCopy && <div className="col-span-4 text-body-sm">{fields?.SubCopy?.value}</div>}
      <CarouselRoot className="col-span-4 w-full mt-[--scale-32]">
        <CarouselContent>
          {fields?.Cards?.map((card: any, index: number) => {
            return (
              <CarouselItem key={card?.id} snapIndex={index} className="basis-[95%]">
                <PdpCard
                  id={card?.id}
                  Title={card?.fields?.Title}
                  Description={card?.fields?.Description}
                  ModuleLabel={card.fields.ModuleLabel}
                  ModuleNumber={card.fields.ModuleNumber}
                />
              </CarouselItem>
            );
          })}
        </CarouselContent>
        {shouldShowCarouselControls && (
          <CarouselControls className="col-span-4 mobile:pt-[--scale-16]">
            <CarouselPagination>
              {fields?.Cards?.map((card: any, paginationIndex: number) => (
                <CarouselPaginationDot key={`${card?.sku}-${paginationIndex}`} snapIndex={paginationIndex} />
              ))}
            </CarouselPagination>
            <div className="flex gap-x-[--scale-16]">
              <CarouselPrevious>
                <ArrowLeftIcon size="lg" />
              </CarouselPrevious>
              <CarouselNext>
                <ArrowRightIcon size="lg" />
              </CarouselNext>
            </div>
          </CarouselControls>
        )}
        <CarouselAnnounce />
      </CarouselRoot>
    </div>
  );
};
