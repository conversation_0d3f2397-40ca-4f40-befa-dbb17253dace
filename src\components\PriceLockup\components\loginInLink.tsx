import { PATHS } from '@/constants';
import { getCurrentUrl } from '@/utils';
import { TextlinkChevronIcon } from '@pmi/catalyst-icons';
import { Link } from '@pmi/catalyst-link';

export const LoginLink: React.FC<{ text?: string }> = ({ text }) => {
  const currentUrl = getCurrentUrl();
  if (!text) return null;
  return (
    <div className="col-span-4 pt-[--scale-16] lg:pt-[--scale-12]">
      <Link href={`${PATHS.LOGIN_WITH_REDIRECT}${currentUrl}`} className="flex items-center gap-[--scale-4]">
        <span className="text-body-xs lg:text-body-md font-medium">{text}</span>
        <TextlinkChevronIcon size="full" className="size-[--scale-16] lg:size-[--scale-20]" />
      </Link>
    </div>
  );
};
