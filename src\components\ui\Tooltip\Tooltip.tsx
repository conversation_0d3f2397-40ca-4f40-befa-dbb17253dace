import React, { PropsWithChildren } from 'react';
import {
  TooltipProvider,
  TooltipRoot,
  TooltipTrigger,
  TooltipPortal,
  Tooltip<PERSON>ontent,
  TooltipArrow,
} from '@pmi/catalyst-tooltip';

import { PopoverContent, PopoverPortal, PopoverRoot, PopoverTrigger } from '@pmi/catalyst-popover';
import { useScreenSize } from '@pmi/www-shared/hooks';
import type { TooltipProps } from './models';

export const Tooltip: React.FC<PropsWithChildren<TooltipProps>> = ({ icon, children }) => {
  const isMobile = useScreenSize();
  if (isMobile) {
    const baseClassName = `
      flex
      py-[--scale-16] px-[--scale-24]
      flex-col
      justify-center
      items-start
      gap-[--scale-8]
      rounded-[--rounded-xs]
      bg-[--fill-off-black-darkest]
      text-[color:--text-inverted]
      data-[state=open]:data-[side=top]:animate-slideDownAndFade 
      data-[state=open]:data-[side=right]:animate-slideLeftAndFade 
      data-[state=open]:data-[side=left]:animate-slideRightAndFade 
      data-[state=open]:data-[side=bottom]:animate-slideUpAndFade  
      shadow-sm will-change-[transform,opacity]
      leading-none select-none outline-none`;

    return (
      <PopoverRoot>
        <PopoverTrigger className="h-[--scale-20]">{icon}</PopoverTrigger>
        <PopoverPortal>
          <PopoverContent sideOffset={5} className={baseClassName} data-testid="popover-content">
            {children}
          </PopoverContent>
        </PopoverPortal>
      </PopoverRoot>
    );
  }

  return (
    <TooltipProvider>
      <TooltipRoot>
        <TooltipTrigger className="h-[--scale-20]">{icon}</TooltipTrigger>
        <TooltipPortal>
          <TooltipContent data-testid="tooltip-content">
            {children}
            <TooltipArrow />
          </TooltipContent>
        </TooltipPortal>
      </TooltipRoot>
    </TooltipProvider>
  );
};
