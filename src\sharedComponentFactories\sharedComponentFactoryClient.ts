import { pipelineComponentName } from '@pmi/shared-component-factory/clientRuntime';
import { mainHandlerAsync } from '@/handlers/clientside';
import './sharedComponentFactory.css';
import sharedComponentFactory from './sharedComponentFactory';

export default (componentName: string, exportName?: string) => {
  if (componentName === pipelineComponentName) {
    return mainHandlerAsync;
  }
  return sharedComponentFactory(componentName);
};
