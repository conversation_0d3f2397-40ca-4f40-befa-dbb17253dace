import React from 'react';
import { withEditorChromes, RichText } from '@sitecore-jss/sitecore-jss-react';
import { useTranslation } from 'react-i18next';
import { withProductFields } from '@/hocs';
import { useIsExperienceEditor, useRouteFields } from '@/hooks';
import { MissingDataSource } from '@/components/common';
import { PRODUCTDETAILPAGES_SECTIONLABELS, PRODUCTTYPES } from '@/constants';
import { CommerceProductBase } from '@/models';
import { getProductType } from '@/utils';
import { ProductOverviewProps } from './models';

const Component: React.FC<ProductOverviewProps> = ({ fields }) => {
  const { t } = useTranslation();
  const product = useRouteFields<CommerceProductBase>();

  const isExperienceEditor = useIsExperienceEditor();
  if (!fields?.OverviewDescription?.value) {
    return isExperienceEditor ? <MissingDataSource /> : null;
  }
  const { OverviewDescription: overview } = fields;
  const productType = getProductType(product);
  let overviewText = overview?.value;
  // Replace {chapter} placeholder if product type is Chapter
  if (productType === PRODUCTTYPES.Chapter) {
    const chapterName = product.Name?.value?.toString() || '';
    overviewText = overviewText.replace('{chapter}', chapterName);
  }
  const sectionLabel =
    productType === PRODUCTTYPES.Chapter
      ? t(PRODUCTDETAILPAGES_SECTIONLABELS.ABOUTCHAPTERS, 'About PMI chapters')
      : t(PRODUCTDETAILPAGES_SECTIONLABELS.OVERVIEW, 'Overview');

  return (
    <div className="grid mobile:grid-cols-4 lg:grid-cols-12 grid-cols pt-[--scale-24] lg:pt-[--scale-80]">
      <h2 className="col-span-4 lg:col-span-5 font-medium text-header-md lg:text-header-lg mobile:mb-[--scale-24]">
        {sectionLabel}
      </h2>
      <RichText
        field={{ ...overview, value: overviewText }}
        tag="div"
        className="rtb-context col-span-4 lg:!col-start-7 lg:col-span-6 font-normal text-body-sm lg:text-body-md"
      />
    </div>
  );
};

export default withEditorChromes(withProductFields(Component));
