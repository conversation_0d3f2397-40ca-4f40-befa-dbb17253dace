import React, { useMemo, useRef } from 'react';
import { ButtonComponent } from '@/components/ui';
import { useSpxHeadlessSettings } from '@/hooks';
import { navigateToUrl } from '@/utils';
import { linkClickTracking } from '@pmi/www-shared/analytics';
import { Course } from '@pmi/www-shared/store';
import { CourseStatus, PriceLockupDatasource } from '../models';
import { usePriceLookupContext } from '../context';

interface CoursesLockupProps {
  course: Course;
}

const CourseStatusLockup: React.FC<CoursesLockupProps> = ({ course }) => {
  const { fields = {} as PriceLockupDatasource } = usePriceLookupContext();
  const continueCourseText = fields.courseInProgressText?.value as string;
  const continueCourseButtonText = fields.courseInProgressButtonText?.value as string;
  const viewInAccountText = fields.courseCompletedText?.value as string;
  const viewInAccountButtonText = fields.courseCompletedButtonText?.value as string;

  const settings = useSpxHeadlessSettings();
  const buttonContainerRef = useRef<HTMLDivElement>(null);
  const displayedText = useMemo(
    () =>
      course?.status === CourseStatus.Completed && course?.progress === 100
        ? viewInAccountText.replace('{pdusTotal}', course?.pdu?.total?.toString())
        : continueCourseText?.replace('{courseProgress}', course?.progress?.toString()),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [course, fields],
  );

  const ctaButtonText = course?.status === CourseStatus.Completed ? viewInAccountButtonText : continueCourseButtonText;

  const handleClick = () => {
    const redirectUrl =
      course?.status === CourseStatus.Completed && course?.progress === 100
        ? `${settings?.MypmiUrl}/library/completed-courses`
        : `${settings?.LMSUrl}/course?id=${course.id}`;
    linkClickTracking(ctaButtonText, buttonContainerRef.current, redirectUrl);
    navigateToUrl(redirectUrl);
  };

  return (
    <>
      <div className="flex flex-col col-span-6 lg:col-span-4">
        <p className="text-body-sm sm:text-body-md font-display">{displayedText}</p>
      </div>
      <div
        className="col-span-4 lg:col-start-5 lg:col-span-2 flex justify-end pt-[--scale-24] lg:pt-0"
        ref={buttonContainerRef}
      >
        <ButtonComponent
          classNames="w-full lg:w-max"
          variant="solid"
          buttonText={ctaButtonText}
          onClick={handleClick}
          danger={false}
          size="sm"
          mobileStickyFooter
        />
      </div>
    </>
  );
};

export default CourseStatusLockup;
