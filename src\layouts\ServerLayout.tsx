import React from 'react';
import { Helmet } from 'react-helmet';
import { ServerLayoutComponentProps } from './models';
import { ServerDataComponent, ScriptComponent, FaviconsComponent, FontsComponent } from './HelperComponents';
import { HtmlIds } from './constants';

export const ServerLayout: React.FC<ServerLayoutComponentProps> = (props) => {
  const helmet = Helmet.renderStatic();
  return (
    <html lang="en" className="dsm">
      <head>
        <FontsComponent />
        <FaviconsComponent />
        <ScriptComponent cssFiles={props.cssFiles} jsFiles={props.jsFiles} deferredJsFiles={props.deferredJsFiles} />
        {helmet.title.toComponent()}
        {helmet.meta.toComponent()}
        {helmet.link.toComponent()}
        {helmet.script.toComponent()}
      </head>
      <body data-is-ssr-rendered="true">
        <div id={HtmlIds.AppRootId} dangerouslySetInnerHTML={{ __html: props.clientLayoutHtml }} />
        <ServerDataComponent
          layout={props.layout}
          routePath={props.routePath}
          dictionary={props.dictionary}
          reduxStore={props.reduxStore}
          processor={props.processor}
        />
      </body>
    </html>
  );
};
