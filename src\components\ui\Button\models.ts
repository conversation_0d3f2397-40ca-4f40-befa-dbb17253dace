import type { ButtonProps, ButtonStylizedProps } from '@pmi/catalyst-button';

export type BaseVariant = ButtonProps['variant'];
export type StylizedVariant = 'primary' | 'secondary' | 'tertiary' | 'quaternary' | 'custom';
export type VariantType = BaseVariant | StylizedVariant;

export type IconPosition = 'left' | 'right';

export interface ButtonComponentProps extends Omit<ButtonProps, 'variant'>, Omit<ButtonStylizedProps, 'variant'> {
  variant: VariantType;
  buttonText?: string;
  icon?: React.JSX.Element;
  iconPosition?: IconPosition;
  ref?: React.RefObject<HTMLButtonElement>;
  mobileStickyFooter?: boolean;
}
