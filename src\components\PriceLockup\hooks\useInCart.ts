import { useEffect, useMemo, useRef, useState } from 'react';
import { useBroadcastChannel } from '@/hooks';
import { useGetCartBaseQuery } from '@/store/graphql';
import { ShoppingCartLineBase, BundleOptionModel } from '@/store/graphql/models';

interface CartBroadcastData {
  productId: string;
  isMembership: boolean;
  selectedBundleOptions: BundleOptionModel[];
}

const transformData = (cartLine?: ShoppingCartLineBase) => ({
  productId: cartLine.product?.productId.toLowerCase(),
  isMembership: cartLine.product?.isMembership ?? false,
  selectedBundleOptions: cartLine.selectedBundleOptions ?? [],
});

export const useInCart = (externalId: string, isAuthenticated: boolean, isMembership: boolean) => {
  const { data, isLoading } = useGetCartBaseQuery({ shouldUpdateCache: false }, { skip: !isAuthenticated });
  const isCached = useRef(true);
  const { message: updatedCartLines } = useBroadcastChannel<CartBroadcastData[]>('spx_cart_cartlines');
  const [cartLinesData, setCartLinesData] = useState<CartBroadcastData[]>([]);

  useEffect(() => {
    if (updatedCartLines !== null) {
      isCached.current = false;
      setCartLinesData(updatedCartLines);
    }
  }, [updatedCartLines]);

  useEffect(() => {
    if (data && !isLoading) {
      const newVal = data.cart.cartLines.map(transformData) ?? [];
      if (data.cart.donationCartLine) {
        newVal.push(transformData(data.cart.donationCartLine));
      }
      setCartLinesData(newVal);
    }
  }, [data, data?.cart.cartLines, isLoading]);

  useEffect(() => {
    if (isLoading) isCached.current = false;
  }, [isLoading]);

  const isInCart = useMemo(() => {
    if (!externalId) return false;
    if (isMembership) {
      return cartLinesData.some((_) => _.isMembership);
    }

    return (
      cartLinesData.some((_) => _.productId === externalId) ||
      cartLinesData.some((cld) => cld.selectedBundleOptions?.some((_) => _.selectionSku === externalId))
    );
  }, [cartLinesData, externalId, isMembership]);

  const isMembershipInCart = useMemo(() => {
    return data?.cart?.cartLines?.some((l) => l.product.isMembership) ?? false;
  }, [data?.cart?.cartLines]);

  return {
    isInCart,
    isMembershipInCart,
    isLoading,
    isCached: isCached.current,
  };
};
