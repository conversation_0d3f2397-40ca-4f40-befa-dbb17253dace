import { useEffect } from 'react';
import { Text, RichText, withEditorChromes } from '@sitecore-jss/sitecore-jss-react';
import { TabsRoot, Ta<PERSON>List, Ta<PERSON>Trigger, TabsContent } from '@pmi/catalyst-tabs';
import { cn } from '@pmi/catalyst-utils';
import { ArrowLeftIcon, ArrowRightIcon } from '@pmi/catalyst-icons';
import { Separator } from '@pmi/catalyst-separator';
import { linkClickTrackingRaw } from '@pmi/www-shared/analytics';
import { TabbedContentProps } from './models';

const Component: React.FC<TabbedContentProps> = ({ content, selected, setSelected }) => {
  useEffect(() => {
    if (!selected) {
      const id = content[0]?.id;
      if (id) {
        setSelected(id);
      }
    }
  }, [content, selected, setSelected]);

  const adobeRegionData = {
    adoberegion: 'pdp-membership-benefits',
  };

  const selectedIndex = content.findIndex((item) => item.id === selected);

  const changeTab = (index: number) => {
    setSelected(content[index].id);
    document.querySelector<HTMLButtonElement>(`[data-index="${index}"]`)?.focus();
  };

  const handleValueChange = (value: string) => {
    const selectedTab = content.find((item) => item?.id === value);
    linkClickTrackingRaw(selectedTab?.Title?.value as string, adobeRegionData.adoberegion, '');
    setSelected(value);
  };

  return (
    <TabsRoot
      value={selected}
      onValueChange={handleValueChange}
      className="w-full text-[--text-primary] mt-[--scale-56]"
    >
      <div className="max-w-screen-2xl mx-auto" {...adobeRegionData}>
        <div className="max-w-screen-xl mx-auto px-6 lg:px-4">
          <TabsList className="w-full border-none">
            {content.map((item, index) => (
              <TabsTrigger
                key={item.id}
                value={item.id}
                data-index={index}
                className={cn(
                  'group',
                  'border-l border-l-[--patterns-border-dm-fill-neutral-2] border-solid',
                  'pl-[--scale-16] pb-[--scale-12] pr-[--scale-24] w-[--scale-224]',
                  'relative transition-colors',
                  'text-start text-header-2xs font-medium text-[--text-neutral-soft]',
                  'data-[state=active]:-left-px',
                  'data-[state=active]:text-[--text-primary]',
                  'data-[state=active]:border-l-[length:--scale-2]',
                  'data-[state=active]:border-l-[--border-off-black-dark]',
                  'from-[--surface-primary] to-[--surface-secondary] data-[state=active]:bg-gradient-to-b',
                )}
              >
                <div
                  className={cn(
                    'rounded-full border border-solid border-[--border-neutral]',
                    'group-data-[state=active]:bg-[--fill-off-black-darkest]',
                    'group-data-[state=active]:border-none w-[--scale-24] h-[--scale-24]',
                    'shrink-0 mb-[--scale-8] items-center justify-center flex flex-col',
                  )}
                >
                  <span
                    className={cn(
                      'group-data-[state=active]:text-[--text-inverted] text-[--text-neutral]',
                      'text-eyebrow font-medium',
                    )}
                  >
                    {index + 1}
                  </span>
                </div>
                <Text field={item.Title} />
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
      </div>
      <Separator className="w-full" />
      <div className="max-w-screen-2xl mx-auto">
        <div className="max-w-screen-xl mx-auto px-6 lg:px-4 pt-[--scale-56]">
          <div className="w-full">
            {content.map((item) => (
              <TabsContent key={item.id} value={item.id} className="min-h-[--scale-384]">
                <RichText field={item.Content} className="rtb-context" />
              </TabsContent>
            ))}
            <div className="flex w-full items-center justify-end h-[--scale-40] mt-[--scale-32]">
              <div className="flex gap-x-[--scale-16]">
                <button
                  type="button"
                  onClick={() => changeTab(selectedIndex - 1)}
                  disabled={selectedIndex === 0}
                  className="transition-opacity disabled:opacity-[--opacity-disabled]"
                >
                  <ArrowLeftIcon />
                  <span className="sr-only">previous tab</span>
                </button>
                <button
                  type="button"
                  onClick={() => changeTab(selectedIndex + 1)}
                  disabled={selectedIndex === content.length - 1}
                  className="transition-opacity disabled:opacity-[--opacity-disabled]"
                >
                  <ArrowRightIcon />
                  <span className="sr-only">next tab</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </TabsRoot>
  );
};

export default withEditorChromes(Component);
