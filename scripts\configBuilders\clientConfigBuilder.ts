import {
  ClientConfigBuilderType,
  hashSeparator,
  polyfillMarker,
  reactScriptRules,
} from '@pmi/shared-component-factory';
import rspack, { Configuration } from '@rspack/core';
import { getResolve } from './getConfigData';

export const clientConfigBuilder: ClientConfigBuilderType = (settings, currentConfig) => {
  const config: Configuration = {
    ...currentConfig,
    output: {
      ...currentConfig.output,
      assetModuleFilename: settings.clientSettings.isProduction
        ? 'public/images/[name]_[hash][ext]'
        : 'public/images/[name][ext]',
    },
    entry: {
      ...(currentConfig.entry as object),
      'client.index': './src/entrypoints/client.index.tsx',
      [polyfillMarker]: './src/entrypoints/client.polyfills.ts',
    },
    module: {
      rules: [
        ...reactScriptRules,
        {
          test: /\.s[ac]ss$/i,
          use: [rspack.CssExtractRspackPlugin.loader, 'css-loader', 'postcss-loader', 'sass-loader'],
        },
        {
          test: /\.css$/i,
          use: [rspack.CssExtractRspackPlugin.loader, 'css-loader', 'postcss-loader'],
        },
        {
          test: /\.(png|jpg)/,
          type: 'asset/resource',
        },
      ],
    },
    optimization: {
      minimize: false, //Minimizing with rspack.SwcJsMinimizerRspackPlugin in plugins
    },
    devtool: settings.clientSettings.isProduction ? 'source-map' : 'eval-source-map',
    resolve: getResolve(),
    plugins: [
      ...currentConfig.plugins!,
      new rspack.optimize.LimitChunkCountPlugin({
        maxChunks: 10,
      }),
      new rspack.CssExtractRspackPlugin({
        filename: settings.clientSettings.isProduction ? `[name]${hashSeparator}[contenthash].css` : '[name].css',
      }),
    ],
  };
  return config;
};
