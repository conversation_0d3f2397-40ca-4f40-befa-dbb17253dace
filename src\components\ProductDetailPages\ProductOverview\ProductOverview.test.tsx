import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ProductOverview } from '.';
import { ProductOverviewProps } from './models';

const mockUseIsExperienceEditor = jest.fn();
const mockUseRouteFields = jest.fn();
const mockGetProductType = jest.fn();

jest.mock('@/hooks', () => ({
  useIsExperienceEditor: () => mockUseIsExperienceEditor(),
  useRouteFields: () => mockUseRouteFields(),
}));

jest.mock('@/utils', () => ({
  getProductType: () => mockGetProductType(),
}));

jest.mock('@sitecore-jss/sitecore-jss-react', () => ({
  withEditorChromes: (Component: React.FC<ProductOverviewProps>) => Component,
  RichText: ({ field, className }: any) => (
    <div data-testid="richtext" className={className}>
      {field?.value}
    </div>
  ),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: any, defaultValue: any) => defaultValue,
  }),
}));

jest.mock('@/components/common', () => ({
  MissingDataSource: () => <div data-testid="missing-datasource">Missing Data Source</div>,
}));

jest.mock('@/hocs', () => ({
  withProductFields: (Component: React.FC<ProductOverviewProps>) => Component,
}));

describe('ProductOverview Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Data Source Handling', () => {
    const props: Partial<ProductOverviewProps> = {
      fields: {
        id: '111',
        subscription_frequency: undefined,
        level_3_hierarchy: [],
      },
    };
    it('should return null when overview is missing and not in Experience Editor', () => {
      mockUseIsExperienceEditor.mockReturnValue(false);
      const { container } = render(<ProductOverview {...props} />);
      expect(container.firstChild).toBeNull();
    });

    it('should show MissingDataSource when overview is missing in Experience Editor', () => {
      mockUseIsExperienceEditor.mockReturnValue(true);
      const { getByTestId } = render(<ProductOverview {...props} />);
      expect(getByTestId('missing-datasource')).toBeInTheDocument();
    });
  });

  describe('Chapter Product Handling', () => {
    beforeEach(() => {
      mockUseIsExperienceEditor.mockReturnValue(false);
      mockGetProductType.mockReturnValue('Chapter Membership');
    });
    const props: Partial<ProductOverviewProps> = {
      fields: {
        id: '111',
        subscription_frequency: undefined,
        level_3_hierarchy: [],
        OverviewDescription: { value: 'Welcome to {chapter}!' },
      },
    };

    it('should replace {chapter} placeholder with chapter name', () => {
      mockUseRouteFields.mockReturnValue({ Name: { value: 'Test Chapter' } });

      const { getByTestId } = render(<ProductOverview {...props} />);
      expect(getByTestId('richtext')).toHaveTextContent('Welcome to Test Chapter!');
    });

    it('should handle missing chapter name gracefully', () => {
      mockUseRouteFields.mockReturnValue({});

      const { getByTestId } = render(<ProductOverview {...props} />);
      expect(getByTestId('richtext')).toHaveTextContent('Welcome to !');
    });
  });

  describe('Section Labels', () => {
    beforeEach(() => {
      mockUseIsExperienceEditor.mockReturnValue(false);
    });
    const props: Partial<ProductOverviewProps> = {
      fields: {
        id: '111',
        subscription_frequency: undefined,
        level_3_hierarchy: [],
        OverviewDescription: { value: 'content' },
      },
    };
    it('should display "About PMI chapters" for chapter products', () => {
      mockUseIsExperienceEditor.mockReturnValue(false);
      mockGetProductType.mockReturnValue('Chapter Membership');

      const { getByText } = render(<ProductOverview {...props} />);
      expect(getByText('About PMI chapters')).toBeInTheDocument();
    });

    it('should display "Overview" for non-chapter products', () => {
      mockUseIsExperienceEditor.mockReturnValue(false);
      mockGetProductType.mockReturnValue('Other');

      const { getByText } = render(<ProductOverview {...props} />);
      expect(getByText('Overview')).toBeInTheDocument();
    });
  });

  describe('Styling and Structure', () => {
    const props: Partial<ProductOverviewProps> = {
      fields: {
        id: '111',
        subscription_frequency: undefined,
        level_3_hierarchy: [],
        OverviewDescription: { value: 'content' },
      },
    };
    it('should apply correct grid classes', () => {
      mockUseIsExperienceEditor.mockReturnValue(false);
      mockGetProductType.mockReturnValue('Other');

      const { container } = render(<ProductOverview {...props} />);
      expect(container.firstChild).toHaveClass('grid');
      expect(container.firstChild).toHaveClass('mobile:grid-cols-4');
      expect(container.firstChild).toHaveClass('lg:grid-cols-12');
    });

    it('should apply correct text classes to RichText component', () => {
      mockUseIsExperienceEditor.mockReturnValue(false);
      mockGetProductType.mockReturnValue('Other');

      const { getByTestId } = render(<ProductOverview {...props} />);
      const richText = getByTestId('richtext');
      expect(richText).toHaveClass('rtb-context');
      expect(richText).toHaveClass('text-body-sm');
      expect(richText).toHaveClass('lg:text-body-md');
    });
  });
});
