import { Text, RichText, withEditorChromes } from '@sitecore-jss/sitecore-jss-react';
import {
  AccordionContent,
  AccordionHeader,
  AccordionItem,
  AccordionRoot,
  AccordionTrigger,
} from '@pmi/catalyst-accordion';
import { MinusIcon, PlusIcon } from '@pmi/catalyst-icons';
import { cn } from '@pmi/catalyst-utils';
import { TabbedContentProps } from './models';

const Component: React.FC<TabbedContentProps> = ({ content, selected, setSelected }) => {
  return (
    <AccordionRoot
      type="single"
      collapsible
      size="md"
      value={selected}
      onValueChange={setSelected}
      className="w-full text-[--text-primary] col-span-4 mt-[--scale-56]"
    >
      {content.map((item, index) => (
        <AccordionItem key={item.id} value={item.id}>
          <AccordionHeader>
            <AccordionTrigger
              className={cn(
                'w-full group',
                'p-[--scale-24]',
                'text-start text-header-2xs font-medium text-[--text-neutral-soft] bg-[--surface-secondary]',
                'data-[state=open]:bg-[--fill-accent-softer]',
                'data-[state=open]:text-[--text-primary]',
              )}
            >
              <div className="flex items-center gap-[--scale-16]">
                <div
                  className={cn(
                    'size-[--scale-32] text-center',
                    'rounded-full border border-solid border-[--border-neutral]',
                    'group-data-[state=open]:bg-[--fill-off-black-darkest]',
                    'group-data-[state=open]:border-none',
                    'shrink-0 pb-[--scale-2] items-center justify-center flex flex-col',
                  )}
                >
                  <span
                    className={cn(
                      'group-data-[state=open]:text-[--text-inverted] text-[--text-neutral]',
                      'text-header-2xs font-medium',
                    )}
                  >
                    {index + 1}
                  </span>
                </div>
                <Text field={item.Title} tag="span" />
              </div>
              <div className="relative size-[--scale-32]">
                <PlusIcon className="absolute inset-0 transition-all group-data-[state=open]:rotate-180 group-data-[state=closed]:opacity-100 group-data-[state=open]:opacity-0" />
                <MinusIcon className="absolute inset-0 transition-all group-data-[state=closed]:opacity-0 group-data-[state=open]:opacity-100" />
              </div>
            </AccordionTrigger>
          </AccordionHeader>
          <AccordionContent className="px-[--scale-24] !py-[--scale-40] bg-gradient-to-b from-[--surface-secondary] to-[--surface-primary]">
            <RichText field={item.Content} className="rtb-context text-body-sm font-normal" />
          </AccordionContent>
        </AccordionItem>
      ))}
    </AccordionRoot>
  );
};

export default withEditorChromes(Component);
