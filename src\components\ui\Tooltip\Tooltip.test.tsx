import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useScreenSize } from '@pmi/www-shared/hooks';
import { Tooltip } from './Tooltip';

jest.mock('@pmi/www-shared/hooks', () => ({
  useScreenSize: jest.fn(),
}));

describe('Tooltip', () => {
  beforeEach(() => {
    global.ResizeObserver = class MockedResizeObserver {
      observe = jest.fn();

      unobserve = jest.fn();

      disconnect = jest.fn();
    };
  });
  afterAll(() => {
    jest.resetAllMocks();
  });
  it('should render popover for mobile', async () => {
    (useScreenSize as jest.Mock).mockReturnValue(true);

    const { getByRole } = render(<Tooltip icon={<i data-testid="popover-icon" />} />);
    const btn = getByRole('button');
    await fireEvent.click(btn);
    expect(screen.getByTestId('popover-content')).toBeInTheDocument();
  });
  it('should render tooltip for desktop', async () => {
    (useScreenSize as jest.Mock).mockReturnValue(false);
    const { getByTestId } = render(<Tooltip icon={<i data-testid="popover-icon" />} />);
    const btn = getByTestId('popover-icon');
    await userEvent.hover(btn);
    await waitFor(() => {
      expect(screen.getByTestId('tooltip-content')).toBeInTheDocument();
    });
  });
});
