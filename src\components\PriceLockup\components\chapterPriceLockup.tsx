import { ButtonComponent } from '@/components/ui';
import { navigateToCartPageWithMembership, navigateToCartPageWithSku, navigateToUrl } from '@/utils';
import { cn } from '@pmi/catalyst-utils';
import { useCallback, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { usePriceLookupContext } from '../context';
import { usePriceLockupLogic } from '../hooks/usePriceLockupLogic';
import { ChapterMembershipPriceLockupProps } from '../models';
import { AlreadyInCart } from './alreadyInCart';
import { LearnMoreLink } from './learnMoreLink';
import { LoginLink } from './loginInLink';
import PriceDisplay from './priceDisplay';

type ChapterState =
  | 'not-authenticated'
  | 'membership-in-cart'
  | 'non-member'
  | 'current-member-no-auto-renew-in-renewal-period'
  | 'current-member-no-auto-renew-outside-renewal-period'
  | 'other-chapter-member';

export const ChapterPriceLockup: React.FC<ChapterMembershipPriceLockupProps> = ({
  studentPrice,
  isSticky,
  learnMoreAboutMembershipLink,
}) => {
  const buttonContainerRef = useRef<HTMLDivElement>(null);
  const { isInCart, isMembershipInCart, isLoading: isCartLoading, isAuthenticated } = usePriceLookupContext();
  const {
    productInfo,
    fields,
    membershipInfo,
    isIndiaStore,
    settings,
    isProductSkuInActiveChapterMemberships,
    isLoading,
  } = usePriceLockupLogic();
  const { t } = useTranslation();
  const { currencyCode, currencySymbol, priceToShow, productSku } = productInfo;
  const { isMember, currentChapter, canRenew, isStudentMember } = membershipInfo;
  const disabledCTA = productInfo.isDeleted === true || isCartLoading || isInCart;
  const currentChapterMember = isProductSkuInActiveChapterMemberships();

  const isWithinRenewalPeriod = useMemo(() => canRenew, [canRenew]);

  const getFormattedExpirationDate = useCallback(() => {
    return currentChapter?.endDate ? new Date(currentChapter.endDate).toLocaleDateString() : '';
  }, [currentChapter?.endDate]);

  const getChapterState = useCallback((): ChapterState => {
    if (!isAuthenticated) return 'not-authenticated';
    if (isMembershipInCart) return 'membership-in-cart';
    if (!isMember) return 'non-member';
    if (!currentChapterMember) return 'other-chapter-member';
    if (!isWithinRenewalPeriod && !isIndiaStore && !isStudentMember)
      return 'current-member-no-auto-renew-outside-renewal-period';
    if (isWithinRenewalPeriod) return 'current-member-no-auto-renew-in-renewal-period'; // this case is so that we can have a default case.

    return null; // default case
  }, [
    currentChapterMember,
    isAuthenticated,
    isMembershipInCart,
    isIndiaStore,
    isMember,
    isStudentMember,
    isWithinRenewalPeriod,
  ]);

  const getButtonConfig = useCallback(() => {
    const state = getChapterState();
    switch (state) {
      case 'non-member':
        return {
          text: t((fields?.becomeMemberButtonText?.value as string) || 'Become a Member'),
          onClick: () => navigateToCartPageWithMembership('individual', window?.location?.pathname),
          show: true,
        };
      case 'current-member-no-auto-renew-outside-renewal-period':
        return {
          text: t((fields?.updateAutoRenewButtonText?.value as string) || 'Update Auto Renew'),
          onClick: () => navigateToUrl(`${settings?.MypmiUrl}/membership`),
          show: true,
        };
      case 'current-member-no-auto-renew-in-renewal-period':
        return {
          text: t((fields?.renewNowButtonText?.value as string) || 'Renew Now'),
          onClick: () => navigateToCartPageWithMembership('individual', window?.location?.pathname),
          show: true,
        };
      default:
        return {
          text: t((fields?.joinChapterButtonText?.value as string) || 'Join Chapter'),
          onClick: () => navigateToCartPageWithSku(productSku, window?.location?.pathname),
          show: true,
        };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getChapterState, t, fields, productSku, settings]);

  const getDescription = useCallback(() => {
    const state = getChapterState();
    const expirationDate = getFormattedExpirationDate();
    switch (state) {
      case 'current-member-no-auto-renew-outside-renewal-period':
        return t(
          (fields?.currentMemberNoAutoRenewText?.value as string)?.replace('{expirationDate}', expirationDate) ||
            // eslint-disable-next-line max-len
            `You are already a member of this chapter! Your membership will expire on ${expirationDate}. Turn on auto renew to never miss a renewal date!`,
        );
      case 'current-member-no-auto-renew-in-renewal-period':
        return t(
          (fields?.currentMemberAutoRenewText?.value as string)?.replace('{expirationDate}', expirationDate) ||
            // eslint-disable-next-line max-len
            `Your membership will expire soon on ${expirationDate}. Renew now to avoid any disruptions to your benefits.`,
        );
      default:
        return t(
          (fields?.chapterDescriptionText?.value as string) ||
            'Auto-renews every year. Cancel anytime. Chapters can only be purchased by existing PMI members.',
        );
    }
  }, [getChapterState, getFormattedExpirationDate, t, fields]);

  const renderPrices = () => {
    const validatePrice = (price: number) => {
      return typeof price === 'number' && !Number.isNaN(price) ? price : null;
    };

    const validPriceToShow = validatePrice(priceToShow);
    const validStudentPrice = validatePrice(studentPrice);

    return (
      <div className={cn('grid grid-cols-4 gap-[--scale-24]', { flex: isSticky })}>
        <div className="col-span-2">
          <PriceDisplay
            title={t((fields?.annualFeeText?.value as string) || 'Annual fee')}
            price={validPriceToShow}
            currencyCode={currencyCode}
            currencySymbol={currencySymbol}
            isSticky={isSticky}
            priceNotification={t((fields?.chapterAnnualPriceNotification?.value as string) || '(prices may vary)')}
          />
        </div>
        <div className="col-span-2">
          <PriceDisplay
            title={t((fields?.studentPriceText?.value as string) || 'Student Price')}
            price={validStudentPrice}
            currencyCode={currencyCode}
            currencySymbol={currencySymbol}
            isSticky={isSticky}
            priceNotification={t((fields?.chapterStudentPriceNotification?.value as string) || '(prices may vary)')}
          />
        </div>
      </div>
    );
  };

  const buttonConfig = useMemo(() => getButtonConfig(), [getButtonConfig]);
  const showLearnMore = useMemo(() => {
    const state = getChapterState();
    return (state === 'non-member' || state === 'not-authenticated') && !isSticky;
  }, [getChapterState, isSticky]);

  if (isLoading || isCartLoading) return null;

  return (
    <>
      <div className="w-full col-span-4 lg:col-span-6 gap-[--scale-24] flex flex-row justify-between">
        <div className="col-span-6 lg:col-span-4">
          {!isIndiaStore
            ? renderPrices()
            : t((fields?.indiaChapterMembershipText?.value as string) || 'Available with a membership')}
          <div className={cn('pt-[--scale-16] lg:pt-[--scale-12]', { hidden: isSticky })}>
            <p className="text-body-xs font-normal text-[--text-neutral-soft] mt-[--scale-4]">
              {!isIndiaStore ? getDescription() : null}
            </p>
          </div>
        </div>
        {buttonConfig.show && (
          <div className="flex items-start shrink-0 md:w-auto" ref={buttonContainerRef}>
            <ButtonComponent
              classNames="w-full lg:whitespace-nowrap"
              variant="solid"
              disabled={disabledCTA}
              buttonText={buttonConfig.text}
              onClick={buttonConfig.onClick}
              danger={false}
              size="sm"
              mobileStickyFooter
            />
          </div>
        )}
      </div>
      <div className="w-full col-span-4 lg:col-span-6 gap-[--scale-12] flex flex-col lg:flex-row lg:justify-between mb-[--scale-16] lg:mb-0">
        {showLearnMore && (
          <>
            <LearnMoreLink
              text={t(fields?.learnMoreText?.value as string)}
              redirectLink={learnMoreAboutMembershipLink.toString()}
            />
            {!isAuthenticated && <LoginLink text={t(fields?.loginLinkText?.value as string)} />}
          </>
        )}
        <AlreadyInCart display={isInCart} />
      </div>
    </>
  );
};
