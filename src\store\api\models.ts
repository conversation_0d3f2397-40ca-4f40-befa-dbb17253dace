import { ImageField } from '@sitecore-jss/sitecore-jss-react';

export interface ProductApiModel {
  sitecoreId: string;
  title: string;
  sku: string;
  regularPrice: number;
  memberPrice: number;
  currencySymbol: string;
  currencyCode: string;
  abstract: string;
  productUrl: string;
  pdus: number;
  leadershipPdus: number;
  strategicBusinessPdus: number;
  technicalPdus: number;
  storeCode: string;
  productType: string;
  heroImage: ImageField;
  heroBackgroundImage: ImageField;
  heroBackgroundImageMobile: ImageField;
  useDarkThemeForHero: boolean;
  badges: ProductApiBadgeModel[];
  wordmark: string;
  parsedTitle: string;
  recommendedAltText: string;
  showPrices?: boolean;
  showCTA?: boolean;
  membershipTextHighlightColor: string;
}

export interface ProductApiBadgeModel {
  value: string;
  color: string;
  variant: string;
  badgeIcon: string;
}

export interface ProductInfoRequest {
  sku: string;
  storeCode: string;
  shouldGetCountryPrices: boolean;
}

export interface ProductInfosRequest {
  skus: string[];
  storeCode: string;
}

export interface ProductInfoResponse {
  data?: ProductApiModel;
  status?: string;
}

export interface ProductInfosResponse {
  data?: ProductApiModel[];
  status?: string;
}

export interface RecommendedProductsRequest {
  sku: string;
  storeCode: string;
}

export interface RecommendedProductsResponse {
  data?: ProductApiModel[];
  status?: string;
}

export interface FreeProductActivationApiRequestModel {
  sku: string;
  qty: number;
}

export interface FreeProductActivationApiResponseModel {
  order_id: string;
}
