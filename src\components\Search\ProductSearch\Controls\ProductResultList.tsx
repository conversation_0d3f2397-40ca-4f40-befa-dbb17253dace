import { useEffect, useState } from 'react';
import { v4 } from 'uuid';
import { ResultListState } from '@coveo/headless';
import { Separator } from '@pmi/catalyst-separator';
import { ResultListProps, ProductResult } from '../models';
import { SearchResult } from './ResultTemplates';

const Component: React.FC<ResultListProps> = ({ controller, storeResolvingCountry, membership }) => {
  const [resultListState, setResultListState] = useState<ResultListState>(controller.state);

  useEffect(() => {
    controller.subscribe(() => setResultListState(controller.state));
  }, []);

  return (
    <>
      {resultListState.results.map((result: ProductResult, index: number) => {
        const isLastItem = index === resultListState.results.length - 1;
        return (
          <div key={v4()} className="w-full">
            <SearchResult result={result} storeResolvingCountry={storeResolvingCountry} membership={membership} />
            {!isLastItem && !result.isTopResult && (
              <div className="px-6">
                <Separator className="mt-8" />
              </div>
            )}
          </div>
        );
      })}
    </>
  );
};

export default Component;
