import React from 'react';
import { PRODUCTDETAILPAGES_FIELDLABELS } from '@/constants';
import { useTranslation } from 'react-i18next';
import { AlreadyChapterMemberProps } from '../models';

const AlreadyChapterMemberLockup: React.FC<AlreadyChapterMemberProps> = ({ activeChapter }) => {
  const { t } = useTranslation();
  const alreadyMemberOfSpecificChapterDictionaryText = t(
    PRODUCTDETAILPAGES_FIELDLABELS.PRICELOCKUP.ALREADYMEMBEROFSPECIFICCHAPTERTEXT,
    'You are already a member of this chapter! Your membership will renew on {00/00/0000}.',
  );

  const alreadyMemberOfSpecificChapterText = alreadyMemberOfSpecificChapterDictionaryText.replace(
    '{00/00/0000}',
    new Date(activeChapter?.endDate).toLocaleDateString(),
  );

  return <div className="col-span-5">{alreadyMemberOfSpecificChapterText}</div>;
};

export default AlreadyChapterMemberLockup;
