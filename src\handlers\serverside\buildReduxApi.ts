import { api as sharedApi } from '@pmi/www-shared/store';
import { PipelineArgs } from '@pmi/shared-component-factory/serverRuntime';
import { api } from '@/store/api/base';
import { graphQLApi } from '@/store/graphql';

export const buildReduxApiAsync = async (args: PipelineArgs) => {
  args.result.reducers[api.reducerPath] = api.reducer;
  args.result.middlewares[api.reducerPath] = api.middleware;

  args.result.reducers[sharedApi.reducerPath] = sharedApi.reducer;
  args.result.middlewares[sharedApi.reducerPath] = sharedApi.middleware;

  args.result.reducers[graphQLApi.reducerPath] = graphQLApi.reducer;
  args.result.middlewares[graphQLApi.reducerPath] = graphQLApi.middleware;
};
