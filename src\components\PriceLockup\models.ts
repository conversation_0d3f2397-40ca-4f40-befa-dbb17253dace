import { ButtonComponentProps } from '@/components/ui';
import { CommerceProductBase, ConfirmationDialogFields } from '@/models';
import * as Jss from '@/models/Jss';
import { ChapterMembership } from '@pmi/www-shared/store';
import { Field, LinkField, TextField } from '@sitecore-jss/sitecore-jss-react';
import { MutableRefObject, ReactNode } from 'react';

export interface PriceLockupProps extends Jss.Rendering<PriceLockupDatasource> {
  isSticky?: boolean;
  containerRef?: MutableRefObject<HTMLDivElement>;
}

export interface PriceLockupDatasource
  extends Jss.BaseDataSourceItem,
    ConfirmationDialogFields,
    MembershipPriceLockupProps,
    ChapterMembershipPriceLockupData {
  learnMoreText: TextField;
  membershipPriceText: TextField;
  fullPriceText: TextField;
  regularPriceText: TextField;
  courseCompletedText: TextField;
  courseCompletedButtonText: TextField;
  courseInProgressText: TextField;
  courseInProgressButtonText: TextField;
  loginLinkText: TextField;
}

export interface PriceDisplayProps {
  title?: string;
  price?: number;
  currencyCode?: string;
  currencySymbol?: string;
  isBold?: boolean;
  isSticky?: boolean;
  priceNotification?: string;
}

export interface StandardPriceLockupProps {
  memberPrice?: number;
  regularPrice?: number;
  currencyCode?: string;
  currencySymbol: string;
  isMember?: boolean;
  hasPurchasedProduct?: boolean;
  isSticky?: boolean;
  sku?: string;
  fields: CommerceProductBase;
  learnMoreAboutMembershipLink?: string;
  learnMoreAboutMembershipLinkText?: string;
}

export interface SinglePriceLockupProps {
  regularPrice: number;
  currencyCode?: string;
  currencySymbol: string;
  isSticky?: boolean;
  sku?: string;
  fields: CommerceProductBase;
}

export interface MembershipProductPriceLockupProps {
  isSticky?: boolean;
}

export interface SingleMembershipProductPriceLockupProps {
  isSticky?: boolean;
}

export interface MemberPriceLockupProps {
  membershipPriceText?: string;
  memberPrice?: number;
  currencySymbol?: string;
  isInCart?: boolean;
}

export interface SubscriptionPriceLockupProps {
  memberPrice: number;
  regularPrice: number;
  currencyCode?: string;
  currencySymbol: string;
  isMember: boolean;
  isSticky?: boolean;
  sku: string;
  learnMoreAboutMembershipLink?: string;
  learnMoreAboutMembershipLinkText?: string;
}

export interface DonationLockupProps {
  currencySymbol?: string;
}

export interface StudentMembershipLockupProps {
  price?: number;
  currencySymbol?: string;
  isRenewal?: boolean;
  renewalDate?: string;
}

export interface ActivateFreeProductButtonProps extends ButtonComponentProps {
  sku?: string;
}

export interface ChapterMembershipPriceLockupProps {
  studentPrice?: number;
  isSticky?: boolean;
  learnMoreAboutMembershipLink?: string;
}

export interface MembershipPriceLockupProps {
  renewNowButtonText?: Field<string>;
  viewInAccountButtonText?: Field<string>;
  youAreAlreadyMemberText?: Field<string>;
  membershipExpiringText?: Field<string>;
  autoRenewText?: Field<string>;
  chapterSelectionText?: Field<string>;
  giftPmiMembershipText?: Field<string>;
  youAreAlreadyStudentMemberText?: Field<string>;
  learnMoreAboutMembershipLink: LinkField;
}

export interface ChapterMembershipPriceLockupData {
  annualFeeText?: TextField;
  studentPriceText?: TextField;
  chapterDescriptionText?: TextField;
  joinChapterButtonText?: TextField;
  becomeMemberButtonText?: TextField;
  learnMoreLink?: TextField;
  indiaChapterMembershipText?: TextField;
  updateAutoRenewButtonText?: TextField;
  currentMemberAutoRenewText?: TextField;
  currentMemberNoAutoRenewText?: TextField;
  chapterAnnualPriceNotification?: TextField;
  chapterStudentPriceNotification?: TextField;
}

export const CourseStatus = {
  Completed: 'Completed',
  InProgress: 'In Progress',
  NotStarted: 'Not Started',
};

export interface PriceLockupBaseProps {
  isSticky: boolean;
  membershipInfo: MembershipInfo;
  productInfo: ProductInfo;
  buttonInfo: ButtonInfo;
  fields: PriceLockupDatasource;
  onButtonClick: () => void;
  renderAdditionalInfo?: () => ReactNode;
  buttonContainerRef?: React.RefObject<HTMLDivElement>;
}

export interface MembershipInfo {
  isMember: boolean;
  canRenew: boolean;
  isStudentMember: boolean;
  expirationDate: string;
  autoRenewStatus: string;
  currentChapter: ChapterMembership;
}

export interface ProductInfo {
  productMembershipType: string;
  currencyCode?: string;
  currencySymbol: string;
  regularPrice: number;
  countryPrice: number;
  priceToShow: number;
  productSku: string;
  hasChapterBundleOption: boolean;
  isSingleMembershipEnabledCountry: boolean;
  productName: string;
  isDeleted: boolean;
  isStudentMembershipProduct: boolean;
}

export interface ButtonInfo {
  text: string;
  isDisabled: boolean;
}

export interface AlreadyChapterMemberProps {
  activeChapter: ChapterMembership;
}
