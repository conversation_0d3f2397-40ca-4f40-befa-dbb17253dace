import React from 'react';
import { Card } from '@pmi/catalyst-card';
import { PdpCardProps } from './models';

export const PdpCard: React.FC<PdpCardProps> = ({ Title, Description, ModuleLabel, ModuleNumber }) => {
  return (
    <div className="col-span-4">
      <Card variant="ghost" className="flex flex-col p-6 bg-[--fill-neutral-softest] h-full min-h-[224px]">
        {ModuleLabel?.value && ModuleNumber?.value && (
          <div className="text-eyebrow font-medium text-[color:--text-neutral-soft]">
            {ModuleLabel?.value} {ModuleNumber?.value}
          </div>
        )}
        <h5 className="text-header-2xs font-bold mt-[--scale-8]">{Title?.value}</h5>
        <div className="text-body-md font-normal text-[color:--text-neutral-soft] mt-[--scale-12]">
          {Description?.value}
        </div>
      </Card>
    </div>
  );
};
