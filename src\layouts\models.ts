import { SelectOptionProps } from '@pmi/www-shared/models';
import { LayoutServiceData } from '@sitecore-jss/sitecore-jss-react';

export interface ClientProcessorType {
  [key: string]: string | undefined;
}

export interface ServerDataComponentProps {
  routePath: string;
  layout: LayoutServiceData;
  dictionary: object;
  reduxStore: object;
  processor: ClientProcessorType;
}

export interface ScriptComponentProps {
  cssFiles: string[];
  jsFiles: string[];
  deferredJsFiles: string[];
}

export interface ServerLayoutComponentProps extends ScriptComponentProps, ServerDataComponentProps {
  clientLayoutHtml: string;
}

export interface PageFields {
  Theme?: SelectOptionProps;
}
