import React from 'react';
import { useGlobalSettings } from '@/hooks';
import { Helmet } from 'react-helmet';

export const DynamicScriptComponent: React.FC = () => {
  const globalSettings = useGlobalSettings();

  // we are loading the adobe scripts in reverse order, because in Sitecore, the Adobe launch script
  // is the first script in the multilist configuration, but we need it to load last- because it is
  // itself loading some LivePerson code that would duplicate other scripts on the page that load after the
  // launch script is loaded.

  return (
    <Helmet>
      <script>window.datalayer_pageload = &#39;off&#39;;</script>
      {globalSettings?.styles?.stylesUrls?.map((_) => <link key={_} href={_} rel="stylesheet" />)}
      {globalSettings?.analytics.adobeUrls
        ?.slice(0)
        ?.reverse()
        ?.map((_) => <script key={_} src={_} />)}
    </Helmet>
  );
};
