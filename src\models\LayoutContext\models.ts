interface Styles {
  stylesUrls: string[];
}

interface Analytics {
  adobeUrls: string[];
}

export interface GlobalSettings {
  analytics: Analytics;
  styles: Styles;
}

export interface SpxHeadlessSettings {
  SheerIdUrl: string;
  SignalRNegotiateUrl: string;
  SignalRResponseWaitTimeoutInSeconds: number;
  SignalRWaitExceedRedirectUrl: string;
  SignalRConnectionRetryCount: string;
  SignalRConnectionRetryTimeoutInSeconds: string;
  LMSUrl: string;
  MypmiUrl: string;
  MembershipSKUMapping: string;
  EnableLogRocketAnalytics: boolean;
  LogRocketProductKey: string;
  EnableLoggingNetworkCalls: boolean;
}

export interface MobileApp {
  isMobileApp: boolean;
}
