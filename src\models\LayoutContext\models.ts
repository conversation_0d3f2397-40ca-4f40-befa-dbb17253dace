interface Styles {
  stylesUrls: string[];
}

interface Analytics {
  adobeUrls: string[];
}

export interface GlobalSettings {
  analytics: Analytics;
  styles: Styles;
}

export interface ABTestConfig {
  team: string; // e.g., "spx"
  version: string; // e.g., "v01"
  variants: string[]; // e.g., ["verA", "verB"]
  enabled: boolean;
  eligibleSkus: string[]; // List of SKUs that should participate in this test (e.g., ["EL106", "EL107"])
}

export interface SpxHeadlessSettings {
  SheerIdUrl: string;
  SignalRNegotiateUrl: string;
  SignalRResponseWaitTimeoutInSeconds: number;
  SignalRWaitExceedRedirectUrl: string;
  SignalRConnectionRetryCount: string;
  SignalRConnectionRetryTimeoutInSeconds: string;
  LMSUrl: string;
  MypmiUrl: string;
  MembershipSKUMapping: string;
  EnableLogRocketAnalytics: boolean;
  LogRocketProductKey: string;
  EnableLoggingNetworkCalls: boolean;
  ABTestConfig?: ABTestConfig; // A/B test configuration
}

export interface MobileApp {
  isMobileApp: boolean;
}
