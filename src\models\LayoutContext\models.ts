interface Styles {
  stylesUrls: string[];
}

interface Analytics {
  adobeUrls: string[];
}

export interface GlobalSettings {
  analytics: Analytics;
  styles: Styles;
}

export interface ABTestConfig {
  testName: string; // e.g., "spx:el106:v01"
  variants: string[]; // e.g., ["verA", "verB"]
  enabled: boolean;
  productSkus?: string[]; // Optional: specific SKUs this test applies to
}

export interface SpxHeadlessSettings {
  SheerIdUrl: string;
  SignalRNegotiateUrl: string;
  SignalRResponseWaitTimeoutInSeconds: number;
  SignalRWaitExceedRedirectUrl: string;
  SignalRConnectionRetryCount: string;
  SignalRConnectionRetryTimeoutInSeconds: string;
  LMSUrl: string;
  MypmiUrl: string;
  MembershipSKUMapping: string;
  EnableLogRocketAnalytics: boolean;
  LogRocketProductKey: string;
  EnableLoggingNetworkCalls: boolean;
  ABTestConfig?: ABTestConfig; // A/B test configuration
}

export interface MobileApp {
  isMobileApp: boolean;
}
