import type { Config } from 'jest';
import { pathsToModuleNameMapper } from 'ts-jest';
import { compilerOptions } from './tsconfig.json';

const config: Config = {
  clearMocks: true,
  collectCoverage: true,
  collectCoverageFrom: ['./src/components/**/*.tsx'],
  coverageDirectory: '<rootDir>/output/jest/coverage',
  coverageReporters: ['text-summary', 'clover', 'json', 'text', 'lcov', 'cobertura'],
  moduleDirectories: ['node_modules', 'config', '<rootDir>'],
  moduleFileExtensions: [
    // Place tsx and ts to beginning as suggestion from Jest team
    // https://jestjs.io/docs/configuration#modulefileextensions-arraystring
    'tsx',
    'ts',
    'web.js',
    'js',
    'web.ts',
    'web.tsx',
    'json',
    'web.jsx',
    'jsx',
    'node',
  ],
  moduleNameMapper: {
    ...pathsToModuleNameMapper(compilerOptions.paths, { prefix: '<rootDir>/src/' }),
  },
  modulePaths: ['<rootDir>/src'],
  preset: 'ts-jest',
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: '<rootDir>/output/jest/tests',
        outputName: 'test-report.xml',
      },
    ],
  ],
  roots: ['<rootDir>/src'],
  setupFilesAfterEnv: ['<rootDir>/config/setupTests.ts'],
  testEnvironment: 'jsdom',
  testPathIgnorePatterns: ['/node_modules/'],
  testRegex: '(/__tests__/.*|(\\.|/)(test|spec))\\.tsx?$',
  transform: {
    '^.+\\.(t|j)sx?$': '@swc/jest',
  },
  testResultsProcessor: 'jest-sonar-reporter',
  globals: {
    SERVER_VARS: {
      API_BASE_URL: '/api',
    },
  },
};

export default config;
