export const tryParseUrlSearch = (searchString: string): Record<string, string> => {
  if (searchString) {
    try {
      const parsedSearchString = searchString.slice(searchString.indexOf('?') + 1);
      const hashes = parsedSearchString ? parsedSearchString.split('&') : [];
      return hashes.reduce((params, hash) => {
        const [key, val] = hash.split('=');
        return {
          ...params,
          [key]: decodeURIComponent(val),
        };
      }, {});
    } catch (error) {
      console.error(`Unable to parse '${searchString}' search string. Error: ${error}`, error);
    }
  }

  return {};
};

export const formatPriceAsString = (price: number, currencyFormat: string) => {
  const absPrice = Math.abs(price);
  const formattedPrice = absPrice.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  return `${currencyFormat}${formattedPrice}`;
};

export const equalPrices = (price1: number, price2: number) => {
  return Math.round(price1 * 100) === Math.round(price2 * 100);
};

export const getFormattedPercent = (price: number) => {
  return price ? `${Math.abs(price).toFixed(0)}%` : `${(0).toFixed(0)}%`;
};

export const stringArrayContains = (arr: Array<string>, element: string) => {
  return arr?.some((str) => {
    return str?.toLowerCase() === element?.toLowerCase();
  });
};

export const isStringInList = (element: string, ...list: Array<string>) => {
  return stringArrayContains(list, element);
};

export const toBoolean = (value: string): boolean => {
  const trimmedValue = value?.trim()?.toLowerCase();
  return trimmedValue === 'true' || trimmedValue === '1';
};

export const toFixedIfNecessary = (value: number, decimalPlaces: number) => (value ? +value.toFixed(decimalPlaces) : 0);

export const parseHtmlTitle = (htmlString: string): string => {
  const withoutSupTags = htmlString.replace(/<sup[^>]*>.*?<\/sup>/gi, '');
  return withoutSupTags.replace(/<[^>]*>/g, '');
};

export const isNullOrUndefined = (value: any) => {
  return typeof value === 'undefined' || value === null;
};

export const stringIsNullOrEmpty = (value: string) => {
  return value == null || value?.toString()?.trim() === '';
};
