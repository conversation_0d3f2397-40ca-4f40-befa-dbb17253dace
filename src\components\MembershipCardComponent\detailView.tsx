import React from 'react';
import { MembershipItemView } from '@/components/Shared';
import { useIsExperienceEditor } from '@/hooks';
import { MissingDataSource } from '@/components/common';
import { ProductApiModel, useGetProductQuery } from '@/store';
import { Text } from '@sitecore-jss/sitecore-jss-react';
import { SpxMembershipCardDataProps } from './models';

const Component: React.FC<SpxMembershipCardDataProps> = ({ fields }) => {
  const { data: productdata, isLoading: isProductLoading } = useGetProductQuery({
    sku: fields?.Sku?.value?.toString(),
    storeCode: null,
    shouldGetCountryPrices: true,
  });
  const isExperienceEditor = useIsExperienceEditor();
  const product = productdata?.data as ProductApiModel;
  const adobeRegion = {
    adoberegion: 'product-card',
  };
  if (isExperienceEditor && !fields?.Sku) return <MissingDataSource />;

  if (!productdata && isExperienceEditor) {
    return (
      <div>
        <div>
          sku : <Text field={fields?.Sku} />
        </div>
      </div>
    );
  }
  if (isProductLoading) return null;

  if (!isProductLoading && !product) return null;

  return (
    <div className="h-full" {...adobeRegion}>
      <MembershipItemView product={product} membershipTextHighlightColor={fields?.membershipTextHighlightColor?.name} />
    </div>
  );
};

export default Component;
