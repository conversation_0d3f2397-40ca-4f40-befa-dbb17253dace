import { FC } from 'react';
import { DotIcon } from '@pmi/catalyst-icons';

interface Props {
  items: string[];
  header: string;
}

export const LanguageList: FC<Props> = ({ header, items }) => {
  if (!items || items.length === 0) {
    return null;
  }
  return (
    <div>
      <span className="text-body-sm">{header}:</span>
      <ul className="list-none space-y-2 text-[length:--scale-14] mt-4">
        {items.map((val) => (
          <li key={val} className="flex items-center text-body-sm">
            <span className="mr-3" aria-hidden="true">
              <DotIcon className="h-[--scale-8] w-[--scale-8]" />
            </span>
            {val}
          </li>
        ))}
      </ul>
    </div>
  );
};
