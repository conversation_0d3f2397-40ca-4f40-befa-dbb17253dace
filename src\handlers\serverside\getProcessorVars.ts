import { PipelineArgs } from '@pmi/shared-component-factory/serverRuntime';

export const getProcessorVarsAsync = async (args: PipelineArgs) => {
  args.result.processorVars.PMI_GRAPHQL_URL = process.env.PMI_GRAPHQL_URL;
  args.result.processorVars.SHARED_API_BASE_URL = process.env.SHARED_API_BASE_URL;
  args.result.processorVars.SITECORE_API_KEY = process.env.SITECORE_API_KEY;
  args.result.processorVars.SITECORE_CONFIG_NAME = process.env.SITECORE_CONFIG_NAME;
  args.result.processorVars.SITECORE_SITE_NAME = process.env.SITECORE_SITE_NAME;
  args.result.processorVars.SITECORE_URL = process.env.SITECORE_URL;
  args.result.processorVars.SPX_API_BASE_URL = process.env.SPX_API_BASE_URL;
};
