$placeholderMappings = @(
 @("www-main","app-main"),@("www-main","app-main")
)
$rootItem = Get-Item -Path master:/sitecore/content/PmiHeadless
$defaultLayout = Get-LayoutDevice "Default"
# Toggle for whether to update Shared or Final Layout
$useFinalLayouts = @($False, $True)
# If set to true, the script will only list the renderings that need fixing, rather than fixing them.
$reportOnly = $False
foreach ( $item in Get-ChildItem -Item $rootItem -Recurse )
{
    # Only interested in items that have a layout
    if (Get-Layout $item)
    {
        foreach( $mapping in $placeholderMappings )
        {
            foreach($useFinalLayout in $useFinalLayouts){
                $useFinalLayout
                # Get renderings in this item that have renderings in the placeholder we want to update 
                $renderings =  Get-Rendering -Item $item -Device $defaultLayout -FinalLayout:$useFinalLayout
                $renderings
                
                foreach ( $rendering in $renderings )
                {
                    # Only update the rendering if we're not in "Report Only" mode
                    if (!$reportOnly)
                    {
                       # Update the placeholder in the rendering and set it back in the item
                       $rendering.Placeholder = $rendering.Placeholder -replace $mapping[0], $mapping[1]
                       Set-Rendering -Item $item -Instance $rendering -FinalLayout:$useFinalLayout
                    }
                    Write-Host "$($item.FullPath) - Rendering $($rendering.UniqueID) - Placeholder: $($mapping[0]) --> $($mapping[1])"
                }
            }
        }
    }
}