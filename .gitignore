[Oo]bj/
src/**/[Bb]in/

# Build results
[Rr]elease/
x64/
[Bb]in/
[Oo]bj/

# Hedgehog Team Development for Sitecore
*.scproj.user
IconCache/
T4RenderCache/
BuiltFiles_*.txt

specs/**/[Bb]in/
_ReSharper.*
packages/
artifacts/
*.user
*.cache
*.suo
*.userprefs
*DS_Store
*.sln.ide
lib/license.xml
lib/Sitecore/*
!lib/Sitecore/readme.MD
lib/System/*
!lib/System/readme.MD
.sonar
vars.user.cmd
/.vs
/target
**/node_modules
temp/
*.ncrunchproject
*.ncrunchsolution
.vscode
nCrunchTemp*.*
.svn/
src/.vs/
/TestResults
/Build_Output
/UpgradeLog.htm
/**/.vs
/**/.vs
build/
clientCode/

\.axoCover/

output
.scannerwork
.idea
