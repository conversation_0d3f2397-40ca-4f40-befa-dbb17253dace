{"compilerOptions": {"module": "ESNext", "target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable", "ESNext"], "baseUrl": "./src", "paths": {"@/*": ["./*"]}, "sourceMap": true, "allowJs": true, "jsx": "react-jsx", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": true, "strictNullChecks": false, "noUnusedLocals": true, "downlevelIteration": true, "resolveJsonModule": true, "skipLibCheck": true, "esModuleInterop": true, "noEmitOnError": false}, "include": ["src/**/*"], "exclude": ["node_modules"], "typeRoots": ["src/node_modules/@types", "src/types/global.d.ts"]}