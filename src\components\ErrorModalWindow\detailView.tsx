import {
  AlertDialogRoot,
  AlertDialogPortal,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
} from '@pmi/catalyst-alert-dialog';
import React from 'react';
import { RichText } from '@sitecore-jss/sitecore-jss-react';
import { Button } from '@pmi/catalyst-button';
import { useAtom } from 'jotai';
import { showFreeProductActivationErrorModalAtom } from '@/atoms';
import { ErrorModalWindowProps } from './models';

export const ErrorModalWindow: React.FC<ErrorModalWindowProps> = ({ fields }) => {
  const [showFreeProductActivationErrorModal, setShowFreeProductActivationErrorModal] = useAtom(
    showFreeProductActivationErrorModalAtom,
  );

  return (
    <AlertDialogRoot open={showFreeProductActivationErrorModal}>
      <AlertDialogPortal>
        <AlertDialogOverlay />
        <AlertDialogContent className="max-w-[800px]">
          <div className="flex text-[--text-primary] font-display gap-[--scale-8] px-[--scale-24] py-[--scale-24]">
            <div className="flex flex-col gap-[--scale-8]">
              <AlertDialogTitle className="text-header-md sm:text-header-lg">{fields?.Title?.value}</AlertDialogTitle>
              <AlertDialogDescription className="text-[--text-secondary] text-body-sm sm:text-body-md">
                <RichText field={fields?.Body} />
              </AlertDialogDescription>
            </div>
          </div>
          <div className="flex justify-end gap-[--scale-8] p-[--scale-24]">
            <AlertDialogAction asChild>
              <Button size="md" onClick={() => setShowFreeProductActivationErrorModal(false)}>
                <span className="text-body-md">{fields?.ButtonText?.value}</span>
              </Button>
            </AlertDialogAction>
          </div>
        </AlertDialogContent>
      </AlertDialogPortal>
    </AlertDialogRoot>
  );
};
