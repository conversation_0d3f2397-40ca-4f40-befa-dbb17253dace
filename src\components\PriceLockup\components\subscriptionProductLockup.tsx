import { FC, useMemo, useRef } from 'react';
import { ButtonComponent } from '@/components/ui';
import { useSpxHeadlessSettings } from '@/hooks';
import { navigateToCartPageWithSku, navigateToUrl } from '@/utils';
import { cn } from '@pmi/catalyst-utils';
import { linkClickTracking } from '@pmi/www-shared/analytics';
import { useGetMembershipsQuery } from '@pmi/www-shared/store';

import { usePriceLookupContext } from '../context';
import { SubscriptionPriceLockupProps } from '../models';
import { usePriceLockupTexts } from '../usePriceLockupTexts';
import { LearnMoreLink } from './learnMoreLink';
import PriceDisplay from './priceDisplay';
import { AlreadyInCart } from './alreadyInCart';

const SubscriptionPriceLockup: FC<SubscriptionPriceLockupProps> = ({
  memberPrice,
  regularPrice,
  currencyCode,
  currencySymbol,
  isMember,
  isSticky,
  sku,
  learnMoreAboutMembershipLink,
  learnMoreAboutMembershipLinkText,
}) => {
  const {
    regularPriceText,
    addToCartButtonText,
    alreadyPurchasedSubsriptionCTA,
    alreadyPurchasedSubscriptionText,
    membershipPriceText,
    includedInMembershipText,
    subscriptionIncludedText,
    autoRenewalText,
  } = usePriceLockupTexts();
  const { product: subscriptionProduct, isAuthenticated, isInCart, isLoading } = usePriceLookupContext();
  const { data: subscriptionData } = useGetMembershipsQuery(null, { skip: !isAuthenticated });
  const settings = useSpxHeadlessSettings();
  const { fullPriceText } = usePriceLockupTexts();
  const buttonContainerRef = useRef<HTMLDivElement>(null);
  const priceText = memberPrice != null && !isMember ? fullPriceText : regularPriceText;
  const isProductIncludedWithMembershipBenefit = subscriptionProduct?.included_in_membership?.value;

  const hasPurchasedSubscriptionProduct = useMemo(() => {
    if (!subscriptionData) return false;

    const subscriptionProducts = [
      subscriptionData.membership,
      ...subscriptionData.subscriptions,
      ...subscriptionData.chapterMemberships,
    ];

    return subscriptionProducts.some((product) => product?.sku === sku);
  }, [sku, subscriptionData]);

  const hasMembershipBenefit = isProductIncludedWithMembershipBenefit && isMember;
  const hasPurchasedSubcription = hasMembershipBenefit || hasPurchasedSubscriptionProduct;

  const ctaButtonText = hasPurchasedSubcription ? alreadyPurchasedSubsriptionCTA : addToCartButtonText;
  const disabledCTA =
    (subscriptionProduct?.IsDeleted?.value === true && !hasPurchasedSubcription) || isLoading || isInCart;

  const renderPrices = () => {
    if (hasMembershipBenefit) {
      return <p className="text-sm font-medium">{subscriptionIncludedText}</p>;
    }

    if (hasPurchasedSubscriptionProduct) {
      return <p className="text-sm font-medium">{alreadyPurchasedSubscriptionText}</p>;
    }

    let priceDisplay = null;
    if (isProductIncludedWithMembershipBenefit) {
      priceDisplay = (
        <div className="col-span-2">
          <h2 className="text-sm font-medium">{membershipPriceText}</h2>
          <div className="text-sm">{includedInMembershipText}</div>
        </div>
      );
    } else if (memberPrice) {
      priceDisplay = (
        <div className="col-span-2">
          <PriceDisplay
            title={membershipPriceText}
            price={memberPrice}
            currencyCode={currencyCode}
            currencySymbol={currencySymbol}
          />
        </div>
      );
    }
    const getSubscriptionFrequency = subscriptionProduct?.subscription_frequency?.name?.toLowerCase() ?? 0;

    return (
      <div className={cn('grid grid-cols-4 gap-[--scale-24]', { flex: isSticky })}>
        {priceDisplay}
        <div className="col-span-2">
          <PriceDisplay
            title={priceText}
            price={regularPrice}
            currencyCode={currencyCode}
            currencySymbol={currencySymbol}
          />
          {!isSticky && subscriptionProduct?.subscription_frequency && (
            <div className="text-sm text-[--text-neutral-soft]">{`${autoRenewalText} ${getSubscriptionFrequency}`}</div>
          )}
        </div>
      </div>
    );
  };

  const handleButtonClick = () => {
    if (hasPurchasedSubcription) {
      linkClickTracking(ctaButtonText, buttonContainerRef.current, `${settings?.MypmiUrl}/account`);
      navigateToUrl(`${settings?.MypmiUrl}/account`);
    } else {
      navigateToCartPageWithSku(sku, window?.location?.pathname);
    }
  };

  return (
    <>
      <div className="col-span-6 lg:col-span-4 flex flex-col">
        {renderPrices()}
        {!isSticky && !isMember && (
          <LearnMoreLink text={learnMoreAboutMembershipLinkText} redirectLink={learnMoreAboutMembershipLink} />
        )}
        <AlreadyInCart display={isInCart} />
      </div>
      <div
        className="col-span-4 lg:col-start-5 lg:col-span-2 flex justify-end pt-[--scale-24] lg:pt-0"
        ref={buttonContainerRef}
      >
        <ButtonComponent
          classNames="w-full lg:w-max"
          variant="solid"
          disabled={disabledCTA}
          buttonText={ctaButtonText}
          onClick={handleButtonClick}
          danger={false}
          size="sm"
          mobileStickyFooter
        />
      </div>
    </>
  );
};

export default SubscriptionPriceLockup;
