import { assembleCompFactoryAsync } from '@pmi/shared-component-factory/clientRuntime';
import { errorBoundaryComponentFactoryWrapper } from '@pmi/www-shared/utils';
import { createStore } from '@pmi/www-shared/store';

export const createComponentFactoryAndStoreAsync = async (reduxState: string) => {
  const { componentFactory: componentFactoryRaw, pipelineFunction } = await assembleCompFactoryAsync();
  const componentFactory = errorBoundaryComponentFactoryWrapper(componentFactoryRaw);
  const apis = {
    reducers: {},
    middlewares: {},
  };
  await pipelineFunction({ methodName: 'BuildReduxApi', result: apis, request: {} });
  const initialReduxStoreState = reduxState ? JSON.parse(reduxState) : undefined;
  const store = createStore(apis.reducers, Object.values(apis.middlewares), initialReduxStoreState);
  return {
    store,
    componentFactory,
  };
};
