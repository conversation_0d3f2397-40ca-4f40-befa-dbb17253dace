import React from 'react';
import {
  type <PERSON><PERSON>ield,
  type LinkFieldValue,
  type LinkProps,
  type RichTextProps,
  type TextField,
} from '@sitecore-jss/sitecore-jss-react';
import {
  WithDatasourceCheckOptions,
  WithDatasourceCheckProps,
} from '@sitecore-jss/sitecore-jss-react/types/enhancers/withDatasourceCheck';
import {
  WithPlaceholderSpec,
  WithPlaceholderOptions,
} from '@sitecore-jss/sitecore-jss-react/types/enhancers/withPlaceholder';
import { type PropsWithChildren } from 'react';

export const Placeholder = ({ children }: PropsWithChildren) => <div id="scplaceholder">{children}</div>;

export const Link = ({ field, onClick, children }: LinkProps) => {
  const { title, target, href } = field as LinkFieldValue;
  return (
    <a id="sc-link" title={title} target={target} href={href} onClick={onClick}>
      {children ?? (field as <PERSON><PERSON>ield).value?.text ?? title}
    </a>
  );
};

export const RichText = ({ field }: RichTextProps) => <div id="sc-richtext">{field?.value ?? ''}</div>;

export const Text = ({ field }: { field: TextField }) => <div id="sc-richtext">{field?.value ?? ''}</div>;

export function withDatasourceCheck(options: WithDatasourceCheckOptions) {
  return function withDatasourceCheckHoc<ComponentProps extends WithDatasourceCheckProps>(
    Component: React.ComponentType<ComponentProps>,
  ) {
    return function WithDatasourceCheck(props: ComponentProps) {
      return <Component {...props} />;
    };
  };
}

export function withPlaceholder(placeholders: WithPlaceholderSpec, options?: WithPlaceholderOptions) {
  return (WrappedComponent: any) => {
    return WrappedComponent;
  };
}
