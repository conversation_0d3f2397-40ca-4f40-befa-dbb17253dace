import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { LanguageList } from './languageList';

jest.mock('@pmi/catalyst-icons', () => ({
  DotIcon: () => <i data-testid="dotIcon">dotIcon</i>,
}));

jest.mock('i18next', () => ({
  t: (text: string) => text,
}));

describe('LanguageList.test', () => {
  it('should be rendered', () => {
    const mockedProps = {
      items: ['Arabic', 'English'],
      header: 'header',
    };
    const { getByText, queryAllByTestId } = render(<LanguageList {...mockedProps} />);
    const dots = queryAllByTestId('dotIcon');
    const engLabel = getByText('English');
    expect(dots.length).toBe(2);
    expect(engLabel).toBeInTheDocument();
  });
  it('should be rendere empty', () => {
    const { container } = render(<LanguageList header="test" items={[]} />);
    expect(container.firstChild).toBeNull();
  });
});
