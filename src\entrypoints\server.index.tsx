import { renderToString } from 'react-dom/server';
import { LayoutServiceData, SitecoreContext } from '@sitecore-jss/sitecore-jss-react';
import {
  defineEnvVariables,
  readClientAssetsForCurrentAppAsync,
  updateMasterCssFileByServerPathAsync,
} from '@pmi/shared-component-factory/serverRuntime';
import { removeSuspenseTraces } from '@pmi/www-shared/utils';
import { renderToPipableStreamAsStringAsync } from '@pmi/www-shared/serverUtils';
import { Provider } from 'react-redux';

import { ClientLayout } from '../layouts/ClientLayout';
import { ServerLayout } from '../layouts/ServerLayout';
import { i18nInitAsync } from './i18n';
import { ViewBag, ViewRenderResults } from './models';
import { createComponentFactoryAndStoreAsync, GetProcessorVarsAsync } from './server.utils';

defineEnvVariables(__dirname, SERVER_VARS.NODE_ENV === 'development');

export const RenderPmiHeadlessAppAsync = async (
  routePath: string,
  data: string | LayoutServiceData,
  viewBag: string | object,
): Promise<{ result: ViewRenderResults }> => {
  try {
    const parsedViewBag: ViewBag = typeof viewBag === 'string' ? JSON.parse(viewBag) : viewBag;
    const layout: LayoutServiceData = typeof data === 'string' ? JSON.parse(data) : data;
    const {
      componentFactory,
      store,
      pipelineFunction,
      recoverableErrors: recoverableApiErrors,
    } = await createComponentFactoryAndStoreAsync(parsedViewBag);

    if (SERVER_VARS.NODE_ENV === 'development') {
      // Only for development; for production this job is delegated to the rendering host
      await updateMasterCssFileByServerPathAsync(__dirname, 'contents');
    }
    const assets = await readClientAssetsForCurrentAppAsync(__dirname, 'readcombined');

    await i18nInitAsync(layout?.sitecore?.context?.language, parsedViewBag.dictionary);

    const { html: clientLayoutHtml, recoverableErrors: recoverableRenderErrors } =
      await renderToPipableStreamAsStringAsync(
        <SitecoreContext layoutData={layout} componentFactory={componentFactory}>
          <Provider store={store}>
            <ClientLayout />
          </Provider>
        </SitecoreContext>,
      );

    const finalHtml = renderToString(
      <ServerLayout
        routePath={routePath}
        clientLayoutHtml={removeSuspenseTraces(clientLayoutHtml)}
        cssFiles={assets.cssAssets}
        deferredJsFiles={assets.jsAssets}
        jsFiles={[]}
        layout={layout}
        dictionary={parsedViewBag?.dictionary}
        reduxStore={store.getState()}
        processor={await GetProcessorVarsAsync(pipelineFunction)}
      />,
    );

    return {
      result: {
        html: `<!DOCTYPE html>${finalHtml}`,
        redirect: null,
        status: 200,
        recoverableApiErrors,
        recoverableRenderErrors,
      },
    };
  } catch (ex) {
    const ssrErrorCode = +process.env.SSR_ERROR_CODE;
    const ssrError = `
      <div>A server JS error has occured</div>
      <div>${ex?.stack}</div>
      <div>${ex?.message}</div>
    `;
    return {
      result: {
        html: ssrErrorCode === 500 ? null : ssrError,
        redirect: null,
        status: ssrErrorCode,
        error: ssrError,
      },
    };
  }
};

export const RenderPmiHeadlessApp = (
  callback: (params: null, result: ViewRenderResults) => void,
  routePath: string,
  data: string,
  viewBag: string,
) => RenderPmiHeadlessAppAsync(routePath, data, viewBag).then((_) => callback(null, _.result));
