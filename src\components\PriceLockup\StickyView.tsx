import React, { useMemo } from 'react';
import { Text } from '@sitecore-jss/sitecore-jss-react';
import { cn } from '@pmi/catalyst-utils';
import { UserStatuses, useUserStatus } from '@pmi/www-shared/hooks';
import { useGetMembershipsQuery } from '@pmi/www-shared/store';
import { useRouteFields } from '@/hooks';
import { isElearningProduct } from '@/utils';
import { CommerceProductBase } from '@/models/Product/models';
import { PriceLockupProps } from './models';
import { DesktopView } from './views';
import { PriceLookupContext, PriceLookupValue } from './context';
import { useInCart } from './hooks/useInCart';

const PriceLockupSticky: React.FC<PriceLockupProps & { contextValue: PriceLookupValue }> = (props) => {
  const { product } = props.contextValue;
  const routeFields = useRouteFields<CommerceProductBase>();
  const isElearning = isElearningProduct(routeFields);
  const userStatus = useUserStatus();
  const isAuthenticated = userStatus === UserStatuses.LoggedIn;
  const { isLoading: subscriptionDataLoading } = useGetMembershipsQuery(null, {
    skip: !isAuthenticated || isElearning,
  });
  const { isInCart, isLoading, isCached, isMembershipInCart } = useInCart(
    product?.ExternalID?.value.toString().toLowerCase(),
    isAuthenticated,
    product?.is_membership.value,
  );

  const contextValue = useMemo<PriceLookupValue>(() => {
    if (!isCached && !isLoading) {
      return {
        ...props.contextValue,
        isAuthenticated,
        isInCart,
        isLoading: false,
        isMembershipInCart,
      };
    }
    return {
      ...props.contextValue,
      isLoading,
      isMembershipInCart,
    };
  }, [props.contextValue, isInCart, isCached, isLoading, isAuthenticated, isMembershipInCart]);

  return (
    <div className="w-full bg-[--surface-primary]">
      <div className="max-w-screen-2xl mx-auto px-4">
        <div
          className={cn('grid grid-cols-12 max-w-screen-xl mx-auto px-4 py-[--scale-24] items-center', {
            'text-[--text-primary]': product?.UseDarkThemeForHero?.value,
          })}
        >
          <div className="col-span-6">
            <h1 className="text-header-sm font-medium">
              <Text field={product.Title} encode={false} />
            </h1>
          </div>
          {!isLoading && !subscriptionDataLoading && (
            <div className="col-start-8 col-span-5 flex items-end">
              <PriceLookupContext.Provider value={contextValue}>
                <DesktopView {...props} />
              </PriceLookupContext.Provider>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export { PriceLockupSticky };
