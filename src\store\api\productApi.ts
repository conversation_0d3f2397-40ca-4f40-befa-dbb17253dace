import { api } from './base';
import {
  RecommendedProductsRequest,
  RecommendedProductsResponse,
  ProductInfoRequest,
  ProductInfoResponse,
  ProductInfosRequest,
  ProductInfosResponse,
  FreeProductActivationApiResponseModel,
  FreeProductActivationApiRequestModel,
} from './models';

const productApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getRecommendedProducts: builder.query<RecommendedProductsResponse, RecommendedProductsRequest>({
      query: (opts) =>
        opts.storeCode
          ? `/product/${opts.sku}/recommended?storeCode=${opts.storeCode}`
          : `/product/${opts.sku}/recommended`,
    }),
    getProduct: builder.query<ProductInfoResponse, ProductInfoRequest>({
      query: (opts) => {
        let url = `/product/${opts.sku}`;
        if (opts.storeCode) {
          url += `?storeCode=${opts.storeCode}`;
        }
        if (opts.shouldGetCountryPrices) {
          url += `?shouldGetCountryPrices=${opts.shouldGetCountryPrices}`;
        }
        return url;
      },
    }),
    getProducts: builder.query<ProductInfosResponse, ProductInfosRequest>({
      query: (opts) => {
        const skus = opts.skus.join(',');
        return opts.storeCode ? `/products?storeCode=${opts.storeCode}&skus=${skus}` : `/products?skus=${skus}`;
      },
    }),
    activateFreeProduct: builder.mutation<FreeProductActivationApiResponseModel, FreeProductActivationApiRequestModel>({
      query: (body) => ({
        url: `/activatefreeproduct`,
        method: 'POST',
        body,
      }),
    }),
  }),
});

export const {
  useGetRecommendedProductsQuery,
  useGetProductQuery,
  useGetProductsQuery,
  useActivateFreeProductMutation,
} = productApi;
