import { Text<PERSON>ield, RichTextField } from '@sitecore-jss/sitecore-jss-react';
import * as Jss from '@/models/Jss';

export interface HeadingWithRichTextProps extends Jss.Rendering<HeadingWithRichTextDataSource> {}

export interface HeadingWithRichTextDataSource extends Jss.BaseDataSourceItem {
  Title: TextField;
  Content: RichTextField;
  renderingParameters: HeadingWithRichTextRenderingParams;
}

export interface HeadingWithRichTextRenderingParams {
  HeadingStyle: {
    id: string;
    name: string;
    fields: {
      Tag: {
        value?: string;
      };
      Class: {
        value?: string;
      };
    };
  };
}
