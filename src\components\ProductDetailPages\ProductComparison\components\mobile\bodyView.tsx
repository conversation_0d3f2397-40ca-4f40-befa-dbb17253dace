import React from 'react';
import { CircleCheckFilledIcon } from '@pmi/catalyst-icons';
import { cn } from '@pmi/catalyst-utils';
import { BodyRow, BodyRowViewProps, BodyColumnValue } from '../../models';

export const BodyView: React.FC<BodyRowViewProps> = ({ rows, highlightedColumn }) => {
  if (!rows || rows.length === 0) {
    return null;
  }

  return (
    <>
      {rows.map((row: BodyRow) => (
        <tr key={row.key} className="odd:bg-[--surface-secondary] text-[--text-primary]">
          {row.columnValues.map((column: BodyColumnValue, columnIndex: number) => {
            const shouldHighlight = columnIndex === highlightedColumn;
            const tdClass = cn('p-0 align-top text-center text-header-xs', {
              '[tr:not(:last-child)_&]:border-x-2 border-[--colors-off-black-dark]': shouldHighlight,
            });
            const divClass = cn('min-h-32 py-6', {
              '[tr:last-child_td_&]:border-x-2 [tr:last-child_td_&]:border-b-2 [tr:last-child_td_&]:rounded-b-3xl border-[--colors-off-black-dark]':
                shouldHighlight,
            });
            return (
              <td key={column.key} className={tdClass}>
                <div className={divClass}>
                  <div className="text-body-md">{row.title}</div>
                  {row.description && <div className="text-body-sm text-[--text-secondary]">{row.description}</div>}
                  <div className="flex justify-center w-full font-medium pt-5">
                    {typeof column.value === 'boolean'
                      ? column.value && <CircleCheckFilledIcon size="lg" />
                      : column.value}
                  </div>
                </div>
              </td>
            );
          })}
        </tr>
      ))}
    </>
  );
};
