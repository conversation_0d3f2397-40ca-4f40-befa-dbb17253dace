import React from 'react';
import { useTranslation } from 'react-i18next';
import { withEditorChromes } from '@sitecore-jss/sitecore-jss-react';
import {
  CarouselRoot,
  CarouselItem,
  CarouselContent,
  CarouselNext,
  CarouselPrevious,
  CarouselControls,
  CarouselAnnounce,
  CarouselPagination,
  CarouselPaginationDot,
} from '@pmi/catalyst-carousel';
import { ArrowLeftIcon, ArrowRightIcon } from '@pmi/catalyst-icons';
import { withProductFields } from '@/hocs';
import { useIsExperienceEditor } from '@/hooks';
import { useScreenSize } from '@pmi/www-shared/hooks';
import { PRODUCTDETAILPAGES_BUTTONLABELS, PRODUCTDETAILPAGES_SECTIONLABELS } from '@/constants';
import { useGetRecommendedProductsQuery } from '@/store';
import { MissingDataSource } from '@/components/common';
import { RecommendedProductsProps } from './models';
import RecommendedProductCard from './itemView';

const Component: React.FC<RecommendedProductsProps> = ({ fields, params }) => {
  const { paginationControlsNumberOfCards = 4 } = params;
  const sku = fields?.ExternalID?.value as string;
  const storeCode = fields?.ProductStore?.fields?.ExternalID?.value as string;
  const { t } = useTranslation();
  const isExperienceEditor = useIsExperienceEditor();
  const isMobile = useScreenSize();
  const { data: products } = useGetRecommendedProductsQuery({ sku, storeCode });

  if (!products || products.data.length === 0) {
    return isExperienceEditor ? <MissingDataSource /> : null;
  }

  const sectionLabel = t(PRODUCTDETAILPAGES_SECTIONLABELS.RECOMMENDEDPRODUCTS, 'You may also like');
  const buttonText = t(PRODUCTDETAILPAGES_BUTTONLABELS.LEARNMORE, 'Learn More');

  const showPaginationControls =
    (isMobile && products.data.length > 1) || products.data.length >= paginationControlsNumberOfCards;
  const adobeRegion = {
    adoberegion: 'product-card',
  };

  return (
    <div {...adobeRegion} className="grid grid-cols-4 lg:grid-cols-12">
      <div className="col-span-4 lg:col-span-12 flex flex-col">
        <h2 className="font-medium text-header-md lg:text-header-lg pb-[--scale-24] lg:pb-[--scale-56]">
          {sectionLabel}
        </h2>
        <CarouselRoot className="w-full">
          <CarouselContent>
            {products.data.map((product, index) => {
              return (
                <CarouselItem key={product.sku} snapIndex={index} className="basis-auto">
                  <div>
                    <RecommendedProductCard product={product} buttonText={buttonText} />
                  </div>
                </CarouselItem>
              );
            })}
          </CarouselContent>

          {showPaginationControls && (
            <div className="grid grid-cols-12 gap-0">
              <CarouselControls className="col-span-12 pt-[--scale-16] lg:pt-[--scale-24]">
                <CarouselPagination>
                  {products.data.map((product, index) => (
                    <CarouselPaginationDot key={product.sku} snapIndex={index} />
                  ))}
                </CarouselPagination>
                <div className="flex gap-x-[--scale-16]">
                  <CarouselPrevious>
                    <ArrowLeftIcon size="lg" />
                  </CarouselPrevious>
                  <CarouselNext>
                    <ArrowRightIcon size="lg" />
                  </CarouselNext>
                </div>
              </CarouselControls>
            </div>
          )}
          <CarouselAnnounce />
        </CarouselRoot>
      </div>
    </div>
  );
};

export default withEditorChromes(withProductFields(Component));
