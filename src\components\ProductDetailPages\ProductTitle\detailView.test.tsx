import { render } from '@testing-library/react';
import { useIsExperienceEditor } from '@/hooks';
import ProductTitle from './detailView';

jest.mock('@/hooks', () => ({
  useIsExperienceEditor: jest.fn(),
}));

jest.mock('@sitecore-jss/sitecore-jss-react', () => ({
  useSitecoreContext: jest.fn(),
  withEditorChromes: (component: any) => component,
  Text: () => {
    return <div>title</div>;
  },
}));

jest.mock('@/hocs', () => ({
  withProductFields: (component: any) => component,
}));

describe('ProductTitle detailView tests', () => {
  it('should be rendered empty', () => {
    (useIsExperienceEditor as jest.Mock).mockReturnValue(false);
    const props = { fields: {} };
    const { container } = render(<ProductTitle {...props} />);

    expect(container.firstChild).toBeNull();
  });

  it('should MissingDataSource be rendered ', () => {
    (useIsExperienceEditor as jest.Mock).mockReturnValue(true);
    const expectedValue = 'This component is missing a data source';
    const props = { fields: {} };
    const { container } = render(<ProductTitle {...props} />);

    expect(container.firstChild).toContainHTML(expectedValue);
  });

  it('should be rendered', () => {
    (useIsExperienceEditor as jest.Mock).mockReturnValue(true);
    const titleValue = 'title';
    const props = {
      fields: {
        Title: {
          value: titleValue,
        },
      },
    };

    const { getByText } = render(<ProductTitle {...props} />);
    const component = getByText(titleValue);

    expect(component).toBeVisible();
  });
});
