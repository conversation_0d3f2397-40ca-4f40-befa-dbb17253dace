import {
  ClientAssetManifestModel,
  ClientConfigBuilderType,
  ClientDevServerConfigBuilderType,
} from '@pmi/shared-component-factory';
import { createHiddenHtmlTag, getClientAssetsForOtherAppsAsync } from '@pmi/shared-component-factory/devserver';
import { clientConfigBuilder } from './clientConfigBuilder';
import { HtmlRspackPlugin } from '@rspack/core';
import { getAbsoluteOutputPath } from './getAbsoluteOutputPath';

export const clientDevConfigBuilder: ClientConfigBuilderType = (settings, currentConfig) => {
  currentConfig = clientConfigBuilder(settings, currentConfig);
  return {
    ...currentConfig,
    mode: 'development',
    devtool: 'inline-source-map',
    optimization: {
      runtimeChunk: 'single',
    },
    cache: false,
    plugins: [
      ...currentConfig.plugins!.filter((_) => !(_ instanceof HtmlRspackPlugin)),
      new HtmlRspackPlugin({
        inject: false,
        templateContent: async ({ htmlRspackPlugin }) => {
          const files = htmlRspackPlugin.files as ClientAssetManifestModel;
          const envVarsTag = createHiddenHtmlTag({
            id: 'spx-website-processor-input',
            name: 'spx-website-processor-input',
            value: JSON.stringify(process.env),
          });
          const clientAssetsForOtherApps = await getClientAssetsForOtherAppsAsync(getAbsoluteOutputPath());
          const jsFiles = [...new Set(files.js)]; //Making js files unique.
          return `
            <!DOCTYPE html>
            <html>
                <head>
                    ${jsFiles
                      .filter((_) => _.indexOf('polyfill.') !== -1)
                      .map((_) => `<script defer src="/${_}"></script>`)
                      .reduce((_p, _c) => `${_p}${_c}`, '')}
                    ${clientAssetsForOtherApps.jsLibs.map((_) => `<script src="${_}"></script>`).reduce((_p, _c) => `${_p}${_c}`, '')}
                    ${clientAssetsForOtherApps.jsComponentFactories.map((_) => `<script src="${_}"></script>`).reduce((_p, _c) => `${_p}${_c}`, '')}
                    
                    ${jsFiles
                      .filter((_) => _.indexOf('.component.') === -1)
                      .filter((_) => _.indexOf('polyfill.') === -1)
                      .map((_) => `<script defer src="/${_}"></script>`)
                      .reduce((_p, _c) => `${_p}${_c}`, '')}
                    ${files.css.map((_) => `<link rel="stylesheet" href="/${_}">`).reduce((_p, _c) => `${_p}${_c}`, '')}
                </head>
                <body>
                      ${envVarsTag}
                </body>
            </html>
          `;
        },
      }),
    ],
  };
};

export const clientDevServerConfigBuilder: ClientDevServerConfigBuilderType = (settings, currentConfig) => ({
  ...currentConfig,
});
