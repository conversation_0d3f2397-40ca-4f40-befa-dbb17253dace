import dotenv from 'dotenv';
import { BuildPipeline } from '@pmi/shared-component-factory';
import { clientConfigBuilder, serverConfigBuilder } from './configBuilders';

dotenv.config({ path: ['./scripts/envs/.env'] });

new BuildPipeline()
  .withServer({
    sharedComponentFactoryPath: '@/sharedComponentFactories/sharedComponentFactoryServer',
    serverConfigBuilder: serverConfigBuilder,
  })
  .withClient({
    sharedComponentFactoryPath: '@/sharedComponentFactories/sharedComponentFactoryClient',
    clientConfigBuilder: clientConfigBuilder,
  })
  .buildAsync()
  .then(
    () => console.info('Server was built successful'),
    (_) => {
      console.error(`Errors while building Server: ${_}`);
      process.exitCode = 1;
    },
  );
