import React from 'react';
import { withEditorChromes, Text } from '@sitecore-jss/sitecore-jss-react';
import { useIsExperienceEditor } from '@/hooks';
import { MissingDataSource } from '@/components/common';
import { withProductFields } from '@/hocs';
import { ProductBylineProps } from './models';

const Component: React.FC<ProductBylineProps> = ({ fields }) => {
  const { Byline: byline } = fields;

  const isExperienceEditor = useIsExperienceEditor();
  if (!byline?.value) {
    return isExperienceEditor ? <MissingDataSource /> : null;
  }

  return (
    <p className="text-body-xs lg:text-body-sm pt-[--scale-12] text-[--text-neutral-soft]">
      <Text field={byline} />
    </p>
  );
};

export default withEditorChromes(withProductFields(Component));
