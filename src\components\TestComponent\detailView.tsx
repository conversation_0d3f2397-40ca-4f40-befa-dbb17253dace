import React from 'react';
import { But<PERSON> } from '@pmi/catalyst-button';
import { TestComponentProps } from './models';

const Component: React.FC<TestComponentProps> = (_props) => {
  return (
    <div>
      <Button>Click me</Button>
      <h1>Hello SPX!</h1>
      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
      <p><PERSON><PERSON><PERSON> aliquet, justo at efficitur aliquam, urna nunc ultrices nunc, id lacinia mauris nunc id nunc.</p>
      <p>Curabitur euismod, mauris id aliquam tincidunt, nunc nunc tincidunt nunc, id lacinia mauris nunc id nunc.</p>
      <p>Donec euismod, mauris id aliquam tincidunt, nunc nunc tincidunt nunc, id lacinia mauris nunc id nunc.</p>
      <p>Quisque euismod, mauris id aliquam tincidunt, nunc nunc tincidunt nunc, id lacinia mauris nunc id nunc.</p>
    </div>
  );
};

export default Component;
