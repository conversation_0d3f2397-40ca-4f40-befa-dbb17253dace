import React from 'react';
import { Helmet } from 'react-helmet';
import { useRouteFields } from '@/hooks';
import { PageMetaData } from '@/models';

export const DynamicMetadataComponent: React.FC = () => {
  const pageMetaData = useRouteFields<PageMetaData>();

  return (
    <Helmet>
      <meta charSet="UTF-8" />
      {pageMetaData?.ExcludeFromExternalSearch?.value && <meta name="robots" content="noindex, nofollow" />}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="msapplication-TileColor" content="#2d89ef" />
      <meta name="msapplication-config" content="/-/media/pmi/icons/favicon/browserconfig.xml" />
      <meta name="theme-color" content="#ffffff" />
    </Helmet>
  );
};
