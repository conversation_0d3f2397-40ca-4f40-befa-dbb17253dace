import { render } from '@testing-library/react';
import BadgeTextView from './badgeTextView';

describe('ProductBadges badgeTextView tests', () => {
  const componentContent = 'some content';
  const expectedStyle = 'from-[--colors-violet-700]';

  it('should have MembershipIcon styles', () => {
    const { getByText } = render(<BadgeTextView icon="MembershipIcon" text={componentContent} />);
    const element = getByText(componentContent);

    expect(element).toHaveClass(expectedStyle);
  });

  it('should not have MembershipIcon styles', () => {
    const { getByText } = render(<BadgeTextView icon="" text={componentContent} />);
    const element = getByText(componentContent);

    expect(element).not.toHaveClass(expectedStyle);
  });
});
