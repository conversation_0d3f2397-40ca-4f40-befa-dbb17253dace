import { createApi } from '@reduxjs/toolkit/query/react';
import { graphqlRequestBaseQuery } from '@rtk-query/graphql-request-base-query';
import { GraphQLClient } from 'graphql-request';
import { CART_QUERY } from './queries';
import { ShoppingCartBaseResponse } from './models';

export const client = new GraphQLClient(`${process.env.PMI_GRAPHQL_URL ?? '/sitecore/api/graphql'}`, {
  credentials: 'include',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    sc_site: process.env.SITECORE_SITE_NAME ?? 'spx-headless',
    sc_apikey: process.env.SITECORE_API_KEY,
  },
});

export const graphQLApi = createApi({
  reducerPath: 'graphQLApi',
  baseQuery: graphqlRequestBaseQuery({ client }),
  endpoints: (builder) => ({
    getCartBase: builder.query<ShoppingCartBaseResponse, { shouldUpdateCache?: boolean }>({
      query: ({ shouldUpdateCache = false } = {}) => {
        return {
          document: CART_QUERY,
          variables: { shouldUpdateCache },
        };
      },
    }),
  }),
});

export const { useGetCartBaseQuery } = graphQLApi;
