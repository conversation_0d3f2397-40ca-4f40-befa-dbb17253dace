import { Raw, Result, ResultList } from '@coveo/headless';
import { MembershipsExtended } from '@pmi/www-shared/store';
import * as Jss from '@/models/Jss';
import { ProductSearchDataDataSource } from '@/models';
import { FacetTypes } from './constants';

export interface ProductSearchProps extends Jss.Rendering<ProductSearchDataDataSource> {}

interface ControllerProps<T> {
  controller: T;
}

export interface ResultListProps extends ControllerProps<ResultList> {
  storeResolvingCountry: string;
  membership: MembershipsExtended;
}

export interface RawResult extends Raw {
  haslayout: string;
  producttitle: string;
  abstract: string;
  productcountrypricing: string;
  productpriceunittype: string;
  globalz32xprice: number;
  productmemberprice: number;
  producttypetitle: string;
}

export interface ProductResult extends Result {
  raw: RawResult;
}

export interface SearchResultProps {
  result: ProductResult;
  storeResolvingCountry: string;
  membership: MembershipsExtended;
}

export interface ProductCountryPricing {
  country_code: string;
  prices: Array<{
    customer_group_id: string;
    customer_group_name: string;
    qty: number;
    price: number;
    renewal_price: number;
  }>;
}

export type FacetChange = {
  facetId: string;
  added: string[];
};

export type NumericFacetChange = {
  facetId: string;
  changed: {
    start: number;
    end: number;
  };
};

export type FacetDictionaryValue = {
  title: string;
  type: (typeof FacetTypes)[keyof typeof FacetTypes];
};
