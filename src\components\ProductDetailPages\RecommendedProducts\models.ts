import { CommerceProductBase } from '@/models';
import * as Jss from '@/models/Jss';
import { ProductApiModel } from '@/store/api/models';

export interface RecommendedProductCardProps {
  product: ProductApiModel;
  buttonText: string;
}

export interface RecommendedProductsRenderingParams extends Jss.BaseRenderingParam {
  paginationControlsNumberOfCards: number;
}

export interface RecommendedProductsProps
  extends Jss.RenderingWithParams<CommerceProductBase, RecommendedProductsRenderingParams> {}
