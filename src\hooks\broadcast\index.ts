import { useEffect, useRef, useState } from 'react';

export const useBroadcastChannel = <T = string>(channelName: string) => {
  const channel = useRef<BroadcastChannel | null>(null);
  const [message, setMessage] = useState<T | null>(null);

  useEffect(() => {
    try {
      channel.current = new BroadcastChannel(channelName);

      const messageHandler = (event: MessageEvent<T>) => {
        setMessage(event.data);
      };

      channel.current?.addEventListener('message', messageHandler);

      return () => {
        if (channel.current) {
          channel.current.removeEventListener('message', messageHandler);
          channel.current.close();
        }
      };
    } catch (error) {
      console.error('Error initializing BroadcastChannel', error);
    }
    return () => false;
  }, [channelName]);

  const postMessage = (msg: T) => {
    if (channel.current) {
      channel.current.postMessage(msg);
    }
  };

  return { message, postMessage };
};
