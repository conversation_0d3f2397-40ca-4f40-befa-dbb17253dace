import React from 'react';
import { CardsHolderProps } from './models';
import { PdpCard } from '../PdpCard';
import { PdpCardProps } from '../PdpCard/models';

export const PdpCardsHolderDesktopView: React.FC<CardsHolderProps> = ({ fields }) => {
  return (
    <div>
      <div className="grid grid-cols-4 lg:grid-cols-12">
        <div className="col-span-4 lg:col-span-5 font-medium text-header-md lg:text-header-lg">
          {fields?.Title?.value}
        </div>
        {fields?.SubCopy && (
          <div className="text-body-md col-span-4 lg:!col-start-7 lg:col-span-6">{fields?.SubCopy?.value}</div>
        )}
      </div>
      <div className="col-span-4 lg:col-span-12 grid grid-cols-4 lg:grid-cols-12 gap-[--scale-8] pt-[--scale-32] lg:pt-[--scale-56]">
        {fields?.Cards?.map((item: any) => {
          const card = item?.fields as PdpCardProps;
          return (
            <PdpCard
              key={card?.id}
              id={card?.id}
              Title={card?.Title}
              ModuleLabel={card?.ModuleLabel}
              ModuleNumber={card?.ModuleNumber}
              Description={card?.Description}
            />
          );
        })}
      </div>
    </div>
  );
};
