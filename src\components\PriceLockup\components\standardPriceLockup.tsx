import React from 'react';
import { cn } from '@pmi/catalyst-utils';
import { ButtonComponent, ButtonComponentProps } from '@/components/ui';
import { useIsFreeProduct } from '@/hooks/Products';
import {
  navigateToCartPageWithSku,
  navigateToLoginWithRedirectToCartPageWithSku,
  navigateToLoginWithMembershipCheckRedirect,
  isElearningProduct,
} from '@/utils';
import PriceDisplay from './priceDisplay';
import { usePriceLockupTexts } from '../usePriceLockupTexts';
import { StandardPriceLockupProps } from '../models';
import { ActivateFreeProductButton } from './activateFreeProductButton';
import { LearnMoreLink } from './learnMoreLink';
import { AlreadyInCart } from './alreadyInCart';
import { usePriceLookupContext } from '../context';

const StandardPriceLockup: React.FC<StandardPriceLockupProps> = ({
  currencyCode,
  currencySymbol,
  fields,
  hasPurchasedProduct,
  isSticky,
  memberPrice,
  regularPrice,
  sku,
  isMember,
  learnMoreAboutMembershipLink,
  learnMoreAboutMembershipLinkText,
}) => {
  const { isAuthenticated, isInCart, isLoading } = usePriceLookupContext();
  const { membershipPriceText, addToCartButtonText, fullPriceText } = usePriceLockupTexts();
  const { isFreeProductActivationEligible } = useIsFreeProduct(fields);
  const hideRegularPrice = isMember && regularPrice >= memberPrice;
  const buttonRenderingProps: ButtonComponentProps = {
    variant: 'solid',
    buttonText: '',
    onClick: () => {},
    size: 'sm',
    classNames: 'mt-2',
    danger: false,
  };
  const disabledCTA = fields?.IsDeleted?.value === true || isLoading || isInCart;

  const handleClick = () => {
    if (!isAuthenticated) {
      if (isElearningProduct(fields) && regularPrice > 0 && memberPrice === 0) {
        navigateToLoginWithMembershipCheckRedirect(sku, window?.location?.pathname);
      } else {
        navigateToLoginWithRedirectToCartPageWithSku(sku, window?.location?.pathname);
      }
    } else {
      navigateToCartPageWithSku(sku, window?.location?.pathname);
    }
  };

  const renderPrices = () => (
    <div className={cn('grid grid-cols-4 gap-[--scale-24]', { flex: isSticky })}>
      <div className="col-span-2">
        <PriceDisplay
          title={membershipPriceText}
          price={memberPrice}
          currencyCode={currencyCode}
          currencySymbol={currencySymbol}
          isBold
        />
      </div>
      {hideRegularPrice ? null : (
        <div className="col-span-2">
          <PriceDisplay
            title={fullPriceText}
            price={regularPrice}
            currencyCode={currencyCode}
            currencySymbol={currencySymbol}
          />
        </div>
      )}
    </div>
  );

  const renderAddToCartButton = () => (
    <div className="col-span-4 lg:col-start-5 lg:col-span-2 flex pt-[--scale-24] lg:pt-0">
      {isFreeProductActivationEligible ? (
        <ActivateFreeProductButton sku={sku} {...buttonRenderingProps} />
      ) : (
        <ButtonComponent
          classNames={cn(buttonRenderingProps.classNames, 'w-full lg:w-max mt-0')}
          disabled={disabledCTA}
          variant={buttonRenderingProps.variant}
          buttonText={addToCartButtonText}
          onClick={handleClick}
          danger={buttonRenderingProps.danger}
          size={buttonRenderingProps.size}
          mobileStickyFooter
        />
      )}
    </div>
  );

  if (hasPurchasedProduct) {
    return (
      <div className="flex flex-col">
        {renderPrices()}
        <div className="mt-2 text-green-600">You have access to this product</div>
      </div>
    );
  }

  return (
    <>
      <div className="col-span-6 lg:col-span-4 flex flex-col">
        {renderPrices()}
        {!isSticky && (
          <LearnMoreLink text={learnMoreAboutMembershipLinkText} redirectLink={learnMoreAboutMembershipLink} />
        )}
        <AlreadyInCart display={isInCart} />
      </div>
      {renderAddToCartButton()}
    </>
  );
};

export default StandardPriceLockup;
