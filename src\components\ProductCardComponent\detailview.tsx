import React from 'react';
import { useGetProductDetailsQuery } from '@pmi/www-shared/store';
import { ProductItemView } from '@/components/Shared';
import { useIsExperienceEditor } from '@/hooks';
import { MissingDataSource } from '@/components/common';
import { ProductApiModel } from '@/store';
import { Text } from '@sitecore-jss/sitecore-jss-react';
import { SpxProductCardDataProps } from './models';

const Component: React.FC<SpxProductCardDataProps> = ({ fields }) => {
  const { data: productdata, isLoading: isProductLoading } = useGetProductDetailsQuery(fields?.Sku?.value?.toString());
  const isExperienceEditor = useIsExperienceEditor();
  const product = productdata?.data as ProductApiModel;
  const adobeRegion = {
    adoberegion: 'product-card',
  };
  if (isExperienceEditor && !fields?.Sku) return <MissingDataSource />;

  if (!productdata && isExperienceEditor) {
    return (
      <div>
        <div>
          sku : <Text field={fields?.Sku} />
        </div>
        <div>
          shortDescription: <Text field={fields?.ShortDescription} />
        </div>
        <div>
          ctaText: <Text field={fields?.buttonText} />
        </div>
      </div>
    );
  }
  if (isProductLoading) return null;

  if (!isProductLoading && !product) return null;

  return (
    <div className="h-full" {...adobeRegion}>
      <ProductItemView
        product={product}
        shortDescription={
          !fields?.ShortDescription?.value ? product.abstract : fields?.ShortDescription?.value?.toString()
        }
        buttonText={fields?.buttonText?.value?.toString()}
        showPrices={fields?.showPrices?.value}
        showCTA={fields?.showCTA?.value}
        membershipTextHighlightColor={fields?.membershipTextHighlightColor?.name}
      />
    </div>
  );
};

export default Component;
