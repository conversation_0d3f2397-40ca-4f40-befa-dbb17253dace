import React from 'react';
import { TextFieldRoot, TextFieldInput, TextFieldSlot } from '@pmi/catalyst-text-field';
import { TextFieldProps } from './models';

export const TextField: React.FC<TextFieldProps> = ({ placeholder, leftIcon, rightIcons, danger, ...rest }) => {
  return (
    <TextFieldRoot danger={danger}>
      {leftIcon && <TextFieldSlot>{leftIcon}</TextFieldSlot>}
      <TextFieldInput placeholder={placeholder} {...rest} />
      {rightIcons && (
        <TextFieldSlot side="right" className="flex gap-[--scale-8]">
          {rightIcons.map((icon, index) => (
            <React.Fragment key={index}>{icon}</React.Fragment>
          ))}
        </TextFieldSlot>
      )}
    </TextFieldRoot>
  );
};
