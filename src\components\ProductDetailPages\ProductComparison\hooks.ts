import { useMemo } from 'react';
import { ProductApiModel } from '@/store';
import { getDisplayPrice, stringIsNullOrEmpty } from '@/utils';
import { BodyRow, Header, ProductComparisonDataSource } from './models';

const getHeaderPrice = (customPrice: string, productData: ProductApiModel): string => {
  if (!stringIsNullOrEmpty(customPrice)) {
    return customPrice;
  }

  return productData ? `${productData.currencySymbol}${getDisplayPrice(productData.regularPrice)}` : '';
};

export const useHeaderData = (
  data: ProductComparisonDataSource,
  products: ProductApiModel[],
  currentPageSku: string,
): Header[] => {
  const result = useMemo(() => {
    if (!data?.productsWithBenefits) {
      return [];
    }

    const headerData: Header[] = [];
    for (let i = 0; i <= data.productsWithBenefits.length - 1; i++) {
      const productWithBenefits = data.productsWithBenefits[i];
      const productData =
        products &&
        products.find((product) => product.sku.toLowerCase() === productWithBenefits.productSku.toLowerCase());

      const isCurrentPageProduct =
        !stringIsNullOrEmpty(currentPageSku) &&
        productWithBenefits.productSku.toLocaleLowerCase() === currentPageSku.toLowerCase();

      headerData.push({
        key: `${productWithBenefits.id}`,
        sku: productWithBenefits.productSku,
        title: !stringIsNullOrEmpty(productWithBenefits?.customTitle)
          ? productWithBenefits.customTitle
          : productData?.parsedTitle,
        description: productWithBenefits.description,
        price: getHeaderPrice(productWithBenefits?.customPrice, productData),
        hidePrice: productWithBenefits?.hidePrice,
        actionLink: {
          text:
            !isCurrentPageProduct &&
            (!stringIsNullOrEmpty(productWithBenefits?.actionLink?.text)
              ? productWithBenefits.actionLink.text
              : data.ProductLinkText.value),
          url:
            !isCurrentPageProduct &&
            (!stringIsNullOrEmpty(productWithBenefits?.actionLink?.url)
              ? productWithBenefits.actionLink.url
              : productData?.productUrl),
        },
      });
    }

    return headerData;
  }, [data, products, currentPageSku]);

  return result;
};

export const useBodyData = (data: ProductComparisonDataSource): BodyRow[] => {
  const result = useMemo(() => {
    if (!data?.productsWithBenefits) {
      return [];
    }

    const bodyData = new Map<string, BodyRow>();
    data.productsWithBenefits.forEach((product) => {
      product.benefits.forEach((benefit) => {
        if (!bodyData.has(benefit.benefitTypeId)) {
          bodyData.set(benefit.benefitTypeId, {
            key: `${benefit.benefitTypeId}`,
            columnValues: [
              {
                key: `${benefit.benefitTypeId}-${product.id}`,
                value:
                  benefit.benefitDataTypeName === 'Checkmark'
                    ? benefit.benefitCheckmarkValue
                    : benefit.benefitTextValue,
              },
            ],
            description: benefit.benefitTypeDescription,
            title: benefit.benefitTypeTitle,
          });
        } else {
          const existingRow = bodyData.get(benefit.benefitTypeId);
          existingRow.columnValues.push({
            key: `${benefit.benefitTypeId}-${product.id}`,
            value:
              benefit.benefitDataTypeName === 'Checkmark' ? benefit.benefitCheckmarkValue : benefit.benefitTextValue,
          });
        }
      });
    });

    return Array.from(bodyData.values());
  }, [data]);

  return result;
};
