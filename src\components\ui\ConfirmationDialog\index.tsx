import { DialogClose, DialogContent, DialogOverlay, DialogPortal, DialogRoot } from '@pmi/catalyst-dialog';
import { cn } from '@pmi/catalyst-utils';
import { Separator } from '@pmi/catalyst-separator';
import { Button, ButtonStylized, ButtonStylizedLabel } from '@pmi/catalyst-button';
import { ArrowRightIcon, CloseIcon } from '@pmi/catalyst-icons';
import { Skeleton } from '@pmi/catalyst-skeleton';
import { useGetUserQuery, UserIdentity } from '@pmi/www-shared/store';
import { replaceTokens } from '@pmi/www-shared/utils';
import { RichText } from '@sitecore-jss/sitecore-jss-react';
import type { ConfirmationDialogProps } from './models';

export const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({ productName = '', adobeRegion, ...props }) => {
  const { confirmFooterText, confirmSuccessText, confirmText, confirmButtonText } = props;
  const { data: user, isLoading: userLoading } = useGetUserQuery();
  const tokens = {
    userEmail: (user as UserIdentity)?.email || '',
    productName,
  };
  const confirmation = replaceTokens(confirmText?.value ?? '', tokens);
  const successMsg = replaceTokens(confirmSuccessText?.value ?? '', tokens);
  const adobeRegionData = {
    adoberegion: adobeRegion,
  };

  return (
    <DialogRoot defaultOpen onOpenChange={props.onCloseCallback}>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent
          {...adobeRegionData}
          autoFocus={false}
          className={cn(
            'fixed top-[50%] left-[50%] h-[480px] w-[312px] lg:w-[932px]',
            'translate-x-[-50%] translate-y-[-50%]',
            'rounded-[--rounded-md] bg-[--surface-primary] pt-[--scale-24] pb-[--scale-32] lg:pb-[--scale-40] focus:outline-none',
            'flex justify-start items-start gap-[--scale-12] font-display',
          )}
        >
          <div className="flex flex-col w-full self-stretch">
            <div className="justify-end items-start inline-flex px-[--scale-24]">
              <DialogClose asChild>
                <Button variant="ghost" size="sm" className="p-0 rounded-[--rounded-xs]">
                  <CloseIcon size="lg" />
                  <span className="sr-only">Close</span>
                </Button>
              </DialogClose>
            </div>
            <div
              className={cn(
                'px-[--scale-24] lg:px-[--scale-40] pt-[--scale-24] flex lg:!flex-row lg:gap-4 flex-col grow shrink',
                'basis-0 justify-between items-start mobile:pb-[--scale-32]',
              )}
            >
              <div className="max-w-full lg:w-[400px] flex flex-col justify-start items-start">
                <p
                  className={cn(
                    'max-w-full bg-gradient-to-b bg-clip-text',
                    'from-[#4F17A8] to-[#190B31]',
                    'font-medium line-clamp-4 lg:line-clamp-5',
                    'text-left text-transparent text-header-sm lg:text-header-lg self-baseline text-wrap break-words',
                  )}
                >
                  {successMsg}
                </p>
              </div>
              <div className="max-w-full lg:w-[334px] flex flex-col justify-start gap-[--scale-16]">
                <p
                  className={cn(
                    'text-body-sm lg:text-body-md',
                    'text-[--text-primary]',
                    'text-wrap break-words line-clamp-4',
                  )}
                >
                  {userLoading ? <Skeleton className="block">Loading</Skeleton> : confirmation}
                </p>
                <div className="w-auto">
                  {props.onConfirm && (
                    <ButtonStylized variant="primary" onClick={props.onConfirm}>
                      <ButtonStylizedLabel>{confirmButtonText?.value}</ButtonStylizedLabel>
                      <ArrowRightIcon size="md" />
                    </ButtonStylized>
                  )}
                </div>
              </div>
            </div>
            <div className="w-full px-[--scale-24] lg:px-[--scale-40] mb-[--scale-12] lg:mb-[--scale-16]">
              <Separator />
            </div>
            <div className="flex self-stretch px-[--scale-24] lg:px-[--scale-40]">
              <div className="flex gap-[--scale-8] items-start">
                <p className="text-[--text-primary]">
                  <div className="rtb-context text-body-xs lg:text-body-sm">
                    <RichText field={confirmFooterText} />
                  </div>
                </p>
              </div>
            </div>
          </div>
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  );
};
