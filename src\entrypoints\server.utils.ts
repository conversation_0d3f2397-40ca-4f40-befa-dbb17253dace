import { assembleCompFactoryAsync } from '@pmi/shared-component-factory/serverRuntime';
import { errorBoundaryComponentFactoryWrapper } from '@pmi/www-shared/utils';
import { createStore } from '@pmi/www-shared/store';
import { ClientProcessorType } from '@/layouts/models';
import { ViewBag } from './models';

type GetPromiseResult<T> = T extends Promise<infer TR> ? TR : never;
type PipelineFuncType = GetPromiseResult<ReturnType<typeof assembleCompFactoryAsync>>['pipelineFunction'];
type ComponentFactoryType = GetPromiseResult<ReturnType<typeof assembleCompFactoryAsync>>['componentFactory'];

export interface CreateComponentFactoryAndStoreResult {
  store: ReturnType<typeof createStore>;
  componentFactory: ComponentFactoryType;
  pipelineFunction: PipelineFuncType;
  recoverableErrors: string;
}

export const createComponentFactoryAndStoreAsync = async (
  viewBag: ViewBag,
): Promise<CreateComponentFactoryAndStoreResult> => {
  const { componentFactory: componentFactoryRaw, pipelineFunction } = await assembleCompFactoryAsync();
  const apis = {
    reducers: {},
    middlewares: {},
  };
  // Building redux API's
  await pipelineFunction({ methodName: 'BuildReduxApi', result: apis, request: {} });
  const store = createStore(apis.reducers, Object.values(apis.middlewares));
  // Prefetching API data
  const prefetchResult = { errors: [] as { error: string; action: string }[] };
  await pipelineFunction({
    methodName: 'PrefetchForSSR',
    result: prefetchResult,
    request: { store, viewBag },
  });
  const recoverableErrors = prefetchResult.errors.map((_) => `Action: ${_.action}, Error: ${_.error}`).join(`
    `);

  const componentFactory = errorBoundaryComponentFactoryWrapper(componentFactoryRaw);
  return {
    store,
    componentFactory,
    pipelineFunction,
    recoverableErrors,
  };
};

type GetProcessorVarsAsyncType = (pipelineFunction: PipelineFuncType) => Promise<ClientProcessorType>;

export const GetProcessorVarsAsync: GetProcessorVarsAsyncType = async (pipelineFunction: PipelineFuncType) => {
  const processorVars: ClientProcessorType = {};
  await pipelineFunction({
    methodName: 'GetProcessorVars',
    result: {
      processorVars,
    },
    request: {},
  });
  return processorVars;
};
