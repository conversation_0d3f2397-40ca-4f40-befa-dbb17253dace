import React, { useState } from 'react';
import { withEditorChromes } from '@sitecore-jss/sitecore-jss-react';
import { useTranslation } from 'react-i18next';
import { useScreenSize } from '@pmi/www-shared/hooks';
import { PRODUCTDETAILPAGES_SECTIONLABELS } from '@/constants';
import TabbedContentDesktop from './desktopView';
import TabbedContentMobile from './mobileView';
import { MembershipBenefitsProps, TabbedContentProps } from './models';

const Component: React.FC<MembershipBenefitsProps> = ({ fields }) => {
  const { t } = useTranslation();
  const isMobile = useScreenSize();
  const tabContent = fields.Items.map((item) => item.fields);
  const [selected, setSelected] = useState<string>(tabContent[0]?.id);

  const sectionLabel = t(PRODUCTDETAILPAGES_SECTIONLABELS.MEMBERSHIPBENEFITS, 'Membership Benefits');

  const props: TabbedContentProps = {
    content: tabContent,
    selected,
    setSelected,
  };

  return (
    <div className="w-full">
      <div className="max-w-screen-2xl mx-auto">
        <div className="max-w-screen-xl mx-auto px-6 lg:px-4">
          <h2 className="text-header-md lg:text-header-lg ">{sectionLabel}</h2>
        </div>
      </div>
      {isMobile ? <TabbedContentMobile {...props} /> : <TabbedContentDesktop {...props} />}
    </div>
  );
};

export default withEditorChromes(Component);
