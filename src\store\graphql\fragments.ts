import { gql } from 'graphql-request';

export const CART_LINE_FRAGMENT_BASE = gql`
  fragment CartLineFragmentShort on Checkout_CartLine {
    id
    displayName
    quantity
    quantityOrdered
    product {
      productId
      isMembership
    }
    selectedBundleOptions {
      optionId
      selectionId
      selectionName
      selectionSku
      selectedProductType
    }
  }
`;

export const CART_FRAGMENT_BASE = gql`
  fragment CartFragment on Checkout_Cart {
    id
    itemsCount
    itemsQuantity
    membershipCartLine {
      ...CartLineFragmentShort
    }
    cartLines {
      ...CartLineFragmentShort
    }
    donationCartLine {
      ...CartLineFragmentShort
    }
  }
  ${CART_LINE_FRAGMENT_BASE}
`;
