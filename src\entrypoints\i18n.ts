import i18n, { InitOptions } from 'i18next';
import { initReactI18next } from 'react-i18next';
import fetchBackend from 'i18next-fetch-backend';

/**
 * Initializes the i18next library to provide a translation dictionary to the app.
 * If your app is not multilingual, this file and references to it can be removed.
 * Elsewhere in the app to use the dictionary `import { t } from 'i18next'; ... t('key')`
 * @param {string} language Optional, the initial language.
 * Only used for SSR; otherwise language set in RouteHandler.
 * @param {*} dictionary Optional, the dictionary to load.
 * Only used for SSR; otherwise, the dictionary is loaded via JSS dictionary service.
 */
export const i18nInitAsync = async (language?: string, dictionary?: string | { [key: string]: string }) => {
  const options: InitOptions = {
    debug: false,
    lng: language ?? 'en',
    fallbackLng: false, // fallback to keys
    load: 'currentOnly', // e.g. don't load 'es' when requesting 'es-MX' -- Sitecore config should handle this
    keySeparator: ';',
    interpolation: {
      escapeValue: false, // not needed for react
    },
  };

  if (dictionary) {
    // if we got dictionary passed, that means we're in a SSR context with a server-provided dictionary
    // so we do not want a backend, because we already know all possible keys
    options.resources = {};
    options.resources[language] = {
      translation: dictionary,
    };

    await i18n.use(initReactI18next).init(options);
  } else {
    // We're running client-side, so we get translation data from the Sitecore dictionary API using fetch backend
    // For higher performance (but less simplicity), consider adding the i18n chained backend to a local cache option
    // like the local storage backend.
    // eslint-disable-next-line max-len
    const dictionaryServicePath = `${process.env.SITECORE_URL}/sitecore/api/jss/dictionary/${process.env.SITECORE_SITE_NAME}/{{lng}}?sc_apikey=${process.env.SITECORE_API_KEY}&sc_site=${process.env.SITECORE_SITE_NAME}`;

    options.backend = {
      loadPath: dictionaryServicePath,
      parse: (data: string) => {
        const parsedData = JSON.parse(data);
        if (parsedData.phrases) {
          return parsedData.phrases;
        }
        return parsedData;
      },
    };

    await i18n.use(fetchBackend).use(initReactI18next).init(options);
  }
};
