import { baseFacetResponseSelector, buildFacetManager, buildSearchEngine } from '@coveo/headless';
import { render } from '@testing-library/react';
import { Facet } from '@pmi/www-shared/components';
import { createFacet } from '@pmi/www-shared/utils';
import { FacetManager } from './FacetManager';

jest.mock('@coveo/headless', () => ({
  ...jest.requireActual('@coveo/headless'),
  buildFacetManager: jest.fn(),
  baseFacetResponseSelector: jest.fn(),
}));
jest.mock('@pmi/www-shared/components', () => ({
  Facet: ({ title }: any) => <div>{title}</div>,
}));

describe('result FacetManager', () => {
  it('should return null for empty facetIds', () => {
    (buildFacetManager as jest.Mock).mockReturnValue({
      subscribe: jest.fn(),
      state: {
        facetIds: [],
      },
    });

    const headlessEngine = buildSearchEngine({
      configuration: {
        organizationId: 'orgId',
        accessToken: '12345',
      },
    });
    const fakeFacet = createFacet(headlessEngine, 'test', 'test');

    const { container } = render(
      <FacetManager controller={headlessEngine}>
        <Facet title="Test" controller={fakeFacet} />
      </FacetManager>,
    );
    expect(container.firstChild).toBeNull();
  });

  it('should match snapshot', () => {
    (buildFacetManager as jest.Mock).mockReturnValue({
      state: {
        facetIds: ['test'],
      },
      subscribe: (callback: () => void) => callback(),
    });
    (baseFacetResponseSelector as jest.Mock).mockReturnValue({
      indexScore: 1,
    });

    const headlessEngine = buildSearchEngine({
      configuration: {
        organizationId: 'orgId',
        accessToken: '12345',
      },
    });

    const { container } = render(
      <FacetManager controller={headlessEngine}>
        <Facet title="Test" controller={createFacet(headlessEngine, 'test', 'test')} />
        <Facet title="Test2" controller={createFacet(headlessEngine, 'test2', 'test2')} />
      </FacetManager>,
    );
    expect(container.firstChild).toMatchInlineSnapshot(`
      <div>
        Test
      </div>
    `);
  });
});
