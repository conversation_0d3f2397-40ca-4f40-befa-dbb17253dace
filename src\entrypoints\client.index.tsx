import Url from 'url-parse';
import { hydrateRoot, createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { LayoutServiceData, SitecoreContext } from '@sitecore-jss/sitecore-jss-react';
import { v4 as uuidv4 } from 'uuid';

import { layoutService } from '../services';
import { ClientLayout } from '../layouts/ClientLayout';
import { i18nInitAsync } from './i18n';
import { HtmlIds, HtmlInputIds } from '../layouts/constants';
import './client.css';
import { createComponentFactoryAndStoreAsync } from './client.utils';

const renderClientAsync = async () => {
  let rootNode = document.getElementById(HtmlIds.AppRootId);
  let hydrate = true;
  if (rootNode == null) {
    rootNode = document.createElement('div');
    rootNode.setAttribute('id', HtmlIds.AppRootId);
    document.body.appendChild(rootNode);
    hydrate = false;
  }
  const layoutInput = document.getElementById(HtmlInputIds.LayoutInputId) as HTMLInputElement;
  const dictionaryInput = document.getElementById(HtmlInputIds.DictionaryInputId) as HTMLInputElement;
  const dictionary = dictionaryInput?.value ? JSON.parse(dictionaryInput.value) : null;
  let layout: LayoutServiceData = null;
  if (layoutInput == null) {
    const url = new Url(window.location.href);
    layout = await layoutService.fetchLayoutData(url.pathname);
    hydrate = false;
  } else {
    layout = JSON.parse(
      (document.getElementById(HtmlInputIds.LayoutInputId) as HTMLInputElement).value,
    ) as LayoutServiceData;
  }
  layout.sitecore.context.currentSessionId = uuidv4();
  if (layout.sitecore.context.pageEditing) {
    hydrate = false;
  }
  let render: (reactNode: React.ReactNode) => void;
  if (hydrate) {
    render = (reactNode) => hydrateRoot(rootNode, reactNode);
  } else {
    render = (reactNode) => createRoot(rootNode).render(reactNode);
  }
  await i18nInitAsync(layout.sitecore.context.language, dictionary);

  const reduxStateInput = document.getElementById(HtmlInputIds.ReduxStoreInputId) as HTMLInputElement;
  const { componentFactory, store } = await createComponentFactoryAndStoreAsync(reduxStateInput?.value);

  render(
    <SitecoreContext componentFactory={componentFactory} layoutData={layout}>
      <Provider store={store}>
        <ClientLayout />
      </Provider>
    </SitecoreContext>,
  );
};

renderClientAsync();
