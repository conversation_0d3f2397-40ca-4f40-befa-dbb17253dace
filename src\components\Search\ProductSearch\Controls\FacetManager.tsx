import {
  SearchEngine,
  Facet as HeadlessFacet,
  FacetManagerPayload,
  buildFacetManager,
  baseFacetResponseSelector,
} from '@coveo/headless';
import React, { useEffect, useState, ReactElement, Children, useMemo } from 'react';

type FacetManagerChild = ReactElement<{ controller: HeadlessFacet }>;

interface FacetManagerProps {
  controller: SearchEngine;
  children: FacetManagerChild | FacetManagerChild[];
}

export const FacetManager: React.FC<FacetManagerProps> = (props) => {
  const { controller } = props;
  const facetManager = buildFacetManager(controller);

  const [state, setState] = useState(facetManager.state);

  useEffect(() => controller.subscribe(() => setState(facetManager.state)), [controller, facetManager.state]);

  const sortedFacets = useMemo(() => {
    function createPayload(facets: FacetManagerChild[]): FacetManagerPayload<FacetManagerChild>[] {
      return facets.map((facet) => ({
        facetId: facet.props.controller.state.facetId,
        payload: facet,
      }));
    }

    const childFacets = Children.toArray(props.children) as FacetManagerChild[];
    const payload = createPayload(childFacets);
    return payload
      .filter(({ facetId }) => state.facetIds.includes(facetId))
      .filter(({ facetId }) => baseFacetResponseSelector(controller.state, facetId).indexScore > 0)
      .map((p) => p.payload);
  }, [controller.state, props.children, state.facetIds]);

  if (sortedFacets.length === 0) return null;

  return sortedFacets;
};
