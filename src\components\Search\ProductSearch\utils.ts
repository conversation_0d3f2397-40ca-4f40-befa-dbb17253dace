import {
  buildQueryExpression,
  buildCriterionExpression,
  buildNumericRange,
  buildDateRange,
  buildRelevanceSortCriterion,
  Facet,
  ResultsPerPage,
  SearchParameters,
  NumericFilter,
  DateFilter,
} from '@coveo/headless';
import { SortOption } from '@pmi/www-shared/components';
import { SitecoreTemplate } from '@/models';
import { SitecoreTemplateIds } from '@/constants';
import { FacetDictionaryValue, ProductCountryPricing, ProductResult } from './models';
import { DEFAULT_SORT, MAX_PDUS, FacetTypes } from './constants';

export const serializeSearchParameters = (searchParams: SearchParameters, sortOptions: SortOption[]) => {
  const hashParams: string[] = [];

  if (searchParams.q) {
    hashParams.push(`q=${searchParams.q}`);
  }

  if (searchParams.sortCriteria) {
    const sortOption = sortOptions.find(
      (option) => buildCriterionExpression(option.criterion) === searchParams.sortCriteria,
    );
    if (sortOption) {
      hashParams.push(`sort=${sortOption.value}`);
    }
  }

  // Text Facet
  if (searchParams.f) {
    Object.keys(searchParams.f).forEach((key) => {
      const value = searchParams.f[key].join(',');
      hashParams.push(`f:${key}=[${value}]`);
    });
  }

  // Date Range Facet
  if (searchParams.df) {
    Object.keys(searchParams.df).forEach((key) => {
      const values = searchParams.df[key];

      if (values.length > 0) {
        const value = [new Date(values[0].start).getTime(), new Date(values[0].end).getTime()];
        hashParams.push(`f:${key}:range=[${value.join(',')}]`);
      }
    });
  }

  // Numeric Range Facet
  if (searchParams.nf) {
    Object.keys(searchParams.nf).forEach((key) => {
      const values = searchParams.nf[key];

      if (values.length > 0) {
        const value = [values[0].start, values[0].end];
        hashParams.push(`f:${key}:range=[${value.join(',')}]`);
      }
    });
  }

  if (searchParams.firstResult) {
    hashParams.push(`first=${searchParams.firstResult.toString()}`);
  }
  if (searchParams.numberOfResults) {
    hashParams.push(`numberOfResults=${searchParams.numberOfResults.toString()}`);
  }
  return hashParams.join('&');
};

export const numberTryParse = (value: string) => {
  const n = parseInt(value, 10);
  return Number.isNaN(n) ? null : n;
};

export const deserializeFacetValues = (value: string) => decodeURIComponent(value).slice(1, -1).split(',');

export const deserializeSearchParameters = (
  facetDictionary: Map<string, FacetDictionaryValue>,
  hash: string,
  sortOptions: SortOption[],
) => {
  const hashParams = new URLSearchParams(hash.slice(1));
  const searchParams: SearchParameters = {
    f: {},
    nf: {},
  };

  const q = hashParams.get('q');
  if (q) searchParams.q = q;

  const sortValue = hashParams.get('sort') || DEFAULT_SORT;
  const sortOption = sortOptions.find((option) => option.value === sortValue.toLowerCase());
  searchParams.sortCriteria = sortOption
    ? buildCriterionExpression(sortOption.criterion)
    : buildCriterionExpression(buildRelevanceSortCriterion());

  const first = numberTryParse(hashParams.get('first'));
  if (first) searchParams.firstResult = first;

  const numberOfResults = numberTryParse(hashParams.get('numberOfResults'));
  if (numberOfResults) searchParams.numberOfResults = numberOfResults;

  hashParams.forEach((value, key) => {
    if (key.startsWith('f:')) {
      if (key.endsWith(':range')) {
        const values = deserializeFacetValues(value);
        const facetKey = decodeURIComponent(key).replace('f:', '').replace(':range', '');
        const facetType = facetDictionary.get(facetKey)?.type || FacetTypes.Numeric;

        switch (facetType) {
          case FacetTypes.Date:
            searchParams.df[facetKey] = [
              buildDateRange({
                start: new Date(parseInt(values[0], 10))
                  .toISOString()
                  .replace('T', '@')
                  .split('.')[0]
                  .replace(/-/g, '/'),
                end: new Date(parseInt(values[1], 10)).toISOString().replace('T', '@').split('.')[0].replace(/-/g, '/'),
                endInclusive: true,
                state: 'selected',
              }),
            ];
            break;
          case FacetTypes.Numeric:
            searchParams.nf[facetKey] = [
              buildNumericRange({
                start: numberTryParse(values[0]),
                end: numberTryParse(values[1]),
                endInclusive: true,
                state: 'selected',
              }),
            ];
            break;
          default:
            break;
        }
      } else {
        const values = deserializeFacetValues(value);
        const facetKey = decodeURIComponent(key).replace('f:', '');
        searchParams.f[facetKey] = values;
      }
    }
  });
  return searchParams;
};

export const serializeInitialAdvancedSearchParameters = (templates: SitecoreTemplate[], storeSitecoreId: string) => {
  const result = buildQueryExpression()
    // Exclude bucket and media folder templates
    .addStringField({
      field: 'z95xtemplate',
      negate: true,
      operator: 'isExactly',
      values: [SitecoreTemplateIds.Bucket, SitecoreTemplateIds.MediaFolder],
    })
    // Include only the templates that are in the templates array
    .addStringField({
      field: 'z95xtemplate',
      operator: 'isExactly',
      values: templates.map((template) => template?.id?.replaceAll('-', '')),
    })
    // Filter by the store sitecore id
    .addExpression(
      buildQueryExpression()
        .addStringField({
          field: 'productz32xstore',
          operator: 'isExactly',
          values: [storeSitecoreId?.replaceAll('-', '')],
        })
        .joinUsing('or')
        .addStringField({
          field: 'productz32xstore',
          operator: 'isExactly',
          values: [''],
        }),
    )
    // Filter for Single Membership
    .addExpression(
      buildQueryExpression()
        .addNumericField({
          field: 'singlez95xmembership',
          negate: true,
          operator: 'isExactly',
          value: 1,
        })
        .joinUsing('or')
        .addStringField({
          field: 'producttypetitle',
          negate: true,
          operator: 'isExactly',
          values: ['Membership'],
        }),
    )
    .toQuerySyntax();

  return result;
};

export const getProductPricing = (result: ProductResult, countryCode: string) => {
  const {
    globalz32xprice: globalPrice,
    productmemberprice: productMemberPrice,
    productcountrypricing: productCountryPricingJson,
  } = result.raw;

  const productCountryPricing: ProductCountryPricing[] = productCountryPricingJson
    ? JSON.parse(productCountryPricingJson)
    : [];
  const countryPricing = productCountryPricing.find((x) => x.country_code === countryCode);
  const hasCountryPricing = countryPricing && countryPricing.prices;
  const nonMemberPricing = hasCountryPricing
    ? countryPricing.prices.find((p) => p.customer_group_name === 'Non-Member')
    : null;
  const nonMemberPrice = !nonMemberPricing ? globalPrice : nonMemberPricing.price;
  const memberPricing = hasCountryPricing
    ? countryPricing.prices.find((p) => p.customer_group_name === 'Individual Members')
    : null;
  const memberPrice = !memberPricing ? productMemberPrice : memberPricing.price;

  return { nonMemberPrice, memberPrice };
};

export const getSelectedFacets = (
  facets: Array<Facet | NumericFilter | DateFilter>,
  facetDictionary: Map<string, FacetDictionaryValue>,
): string => {
  const formattedFacets: string[] = [];

  facets.forEach((facet) => {
    const facetDictionaryValue = facetDictionary.get(facet.state.facetId);
    const facetType = facetDictionaryValue?.type || FacetTypes.Text;
    const displayName = facetDictionaryValue?.title || facet.state.facetId;

    switch (facetType) {
      case FacetTypes.Numeric: {
        const f = <NumericFilter>facet;
        const { start, end } = f.state.range || { start: 0, end: MAX_PDUS };
        if (+start > 0 || +end < MAX_PDUS) {
          formattedFacets.push(`${displayName}:${start}-${end}`);
        }
        break;
      }
      case FacetTypes.Text:
      default: {
        const f = <Facet>facet;
        const selectedValues = f.state.values.filter((value) => value.state === 'selected');
        if (selectedValues.length > 0) {
          const valueList = selectedValues.map((v) => v.value).join(',');
          formattedFacets.push(`${displayName}:${valueList}`);
        }
      }
    }
  });

  return formattedFacets.join('|');
};

export const getPageFirstResult = (pageParams: Record<string, unknown>) => {
  return pageParams?.firstResult || 0;
};

export const getCurrentPageNumber = (currentPageFirstResult: any, resultsPerPage: ResultsPerPage) => {
  return ((currentPageFirstResult || 0) as number) / resultsPerPage.state.numberOfResults + 1;
};

export const getBreadcrumbsFacetDictionary = (originalMap: Map<string, FacetDictionaryValue>): Map<string, string> => {
  const newMap = new Map();
  Array.from(originalMap.entries()).forEach(([key, value]) => {
    newMap.set(key, value.title);
  });
  return newMap;
};
