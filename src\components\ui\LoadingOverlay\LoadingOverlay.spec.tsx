import { render } from '@testing-library/react';
import { PropsWithChildren } from 'react';
import { LoadingOverlay } from './LoadingOverlay';

jest.mock('@sitecore-jss/sitecore-jss-react');
jest.mock('@pmi/catalyst-spinner', () => ({
  Spinner: () => <div data-testId="dsm-spinner" />,
}));

jest.mock('@pmi/catalyst-dialog', () => ({
  ...jest.requireActual('@pmi/catalyst-dialog'),
  DialogContent: ({ children }: PropsWithChildren) => <div id="dialog-content">{children}</div>,
  DialogOverlay: () => <div id="dialog-overlay" />,
  DialogPortal: ({ children }: PropsWithChildren) => <div id="dialog-protal">{children}</div>,
  DialogRoot: ({ children }: PropsWithChildren) => <div id="dialog-root">{children}</div>,
  DialogTitle: ({ children }: PropsWithChildren) => <span>{children}</span>,
}));

describe('LoadingOverlay', () => {
  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should be rendered', () => {
    // eslint-disable-next-line react/jsx-boolean-value
    const { container } = render(<LoadingOverlay open={true} description="test desc" />);
    expect(container.firstChild).toMatchSnapshot();
  });
});
