import { Button } from '@pmi/catalyst-button';
import { Skeleton } from '@pmi/catalyst-skeleton';

export const PriceLockupLoadingView = () => {
  return (
    <>
      <div className="grid grid-cols-3 gap-3 mb-3 mt-6">
        <div>
          <Skeleton className="block w-full mb-1 h-[--scale-24]" loading>
            Loading
          </Skeleton>
          <Skeleton className="block w-2/3 h-[--scale-24]">Loading</Skeleton>
        </div>
        <div>
          <Skeleton className="block w-full mb-1 h-[--scale-24]">Loading</Skeleton>
          <Skeleton className="block w-2/3 h-[--scale-24]">Loading</Skeleton>
        </div>
        <div className="pl-6">
          <Skeleton>
            <Button className="w-full h-full">Button</Button>
          </Skeleton>
        </div>
      </div>
      <div>
        <Skeleton className="block w-full mb-1 h-[--scale-16]" loading>
          Loading
        </Skeleton>
        <Skeleton className="block w-2/3 h-[--scale-16]">Loading</Skeleton>
        <Skeleton className="mt-3">
          <Button className="h-[--scale-24]">Button</Button>
        </Skeleton>
      </div>
    </>
  );
};
