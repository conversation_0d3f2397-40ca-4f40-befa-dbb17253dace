import { ComponentRendering } from '@sitecore-jss/sitecore-jss-react';

export interface ViewRenderResults {
  html: string;
  redirect: null | string;
  status: number;
  error?: string;
  recoverableApiErrors?: string;
  recoverableRenderErrors?: string;
}

export interface ViewBag {
  dictionary: { [key: string]: string };
  httpContext: HttpContext;
  initialData: object;
}

export interface HttpContext {
  request: HttpRequest;
}

export interface HttpRequest {
  url: string;
}

export interface ComponentData {
  sitecore: {
    rendering: ComponentRendering;
  };
}
