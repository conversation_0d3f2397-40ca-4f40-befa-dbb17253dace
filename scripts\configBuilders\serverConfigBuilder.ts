import { ServerConfigBuilderType, reactScriptRules } from '@pmi/shared-component-factory';
import rspack, { CopyRspackPlugin } from '@rspack/core';
import { getResolve } from './getConfigData';
import defineServerVars from './defineServerVars';

export const serverConfigBuilder: ServerConfigBuilderType = (settings, currentConfig) => {
  const config = {
    ...currentConfig,
    output: {
      ...currentConfig.output,
      assetModuleFilename: settings.serverSettings.isProduction
        ? 'public/images/[name]_[hash][ext]'
        : 'public/images/[name][ext]',
    },
    entry: {
      ...(currentConfig.entry as object),
      'server.bundle': './src/entrypoints/server.index.tsx',
    },
    module: {
      rules: [
        ...reactScriptRules,
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader'],
        },
        {
          test: /\.(png|jpg)/,
          type: 'asset/resource',
        },
      ],
    },
    resolve: getResolve(),
    plugins: [
      ...currentConfig.plugins!,
      new rspack.optimize.LimitChunkCountPlugin({
        maxChunks: 1,
      }),
      new rspack.DefinePlugin({
        __WEBPACK_EXTERNAL_createRequire: '(() => require)',
      }),
      new CopyRspackPlugin({
        patterns: [
          {
            from: '.env',
            to: `${currentConfig.output!.path}/envs`,
            context: './src/envs',
          },
          settings.serverSettings.isProduction
            ? null
            : {
                from: '.development.env',
                to: `${currentConfig.output!.path}/envs`,
                context: './src/envs',
              },
        ].filter((_) => !!_),
      }),
      defineServerVars(),
    ],
  };
  return config;
};
