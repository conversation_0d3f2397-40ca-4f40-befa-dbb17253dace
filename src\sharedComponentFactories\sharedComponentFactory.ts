import * as Components from '@/components';
import { PriceLockupSticky } from '@/components/PriceLockup/StickyView';
import * as Containers from '@/containers';
import { StickyComponents } from '@/layouts/constants';
import { ComponentType } from 'react';
import './sharedComponentFactory.css';

const factory = new Map<string, ComponentType<any>>();

// Containers
factory.set('SPX Product Detail Page Container', Containers.ProductDetailPageContainer);

// Components
factory.set('SPX TestComponent', Components.TestComponent);
factory.set('SPX CTA Button', Components.CTAButton);
factory.set('SPX Product Title', Components.ProductTitle);
factory.set('SPX Product Byline', Components.ProductByline);
factory.set('SPX Product Short Description', Components.ProductShortDescription);
factory.set('SPX Course Detail', Components.CourseDetail);
factory.set('SPX Price Lockup Component', Components.PriceLockup);
factory.set('SPX PDPBreadcrumbs', Components.Breadcrumbs);
factory.set('SPX Pdp Action Link', Components.PdpActionLink);
factory.set('SPX Product Overview', Components.ProductOverview);
factory.set('SPX Product Overview Languages', Components.ProductOverviewLanguages);
factory.set('SPX Pdp Cards Holder', Components.PdpCardsHolder);
factory.set('SPX Pdp Card', Components.PdpCard);
factory.set('SPX PDP Recommended Products', Components.RecommendedProducts);
factory.set('SPX Online Course Breakdown Header', Components.OnlineCourseBreakdownHeader);
factory.set('SPX Online Course Breakdown Content', Components.OnlineCourseBreakdownContent);
factory.set('SPX Product Hero', Components.ProductHero);
factory.set('SPX Link CTA', Components.LinkCta);
factory.set('SPX Basic Product Information Card', Components.BasicProductInfoCard);
factory.set('SPX Membership Benefits', Components.MembershipBenefits);
factory.set('SPX Heading with RichText', Components.HeadingWithRichText);
factory.set('SPX Get Access Button', Components.GetAccessButton);
factory.set('SPX Subscription Chart', Components.SubscriptionChart);
factory.set('SPX Adobe Product Tracking', Components.AdobeProductTracking);
factory.set('SPX AB Impression Tracking', Components.ABImpressionTracking);
factory.set('SPX Product Badges', Components.ProductBadges);
factory.set('SPX Product Container', Components.ProductContainer);
factory.set('SPX Product Card', Components.ProductCardComponent);
factory.set('SPX Error Modal Window', Components.ErrorModalWindow);
factory.set('SPX Product Comparison', Components.ProductComparison);
factory.set('SPX Chapter Contact Information', Components.ChapterContactInformation);
factory.set('SPX Chapter Charter Information', Components.ChapterCharterInformation);
factory.set('SPX Pdp Chapter Action Link', Components.PdpChapterActionLink);
factory.set('SPX Pay Later Message', Components.PayLaterMessage);
factory.set('SPX PDP Language Selection', Components.PdpLanguageSelection);
factory.set('SPX Membership Card', Components.MembershipCardComponent);

// Sticky components:
factory.set(StickyComponents.PriceLockup, PriceLockupSticky);

// Search
factory.set('SPX Product Search', Components.ProductSearch);

export default (componentName: string, exportName?: string) => factory.get(componentName) as ComponentType;
