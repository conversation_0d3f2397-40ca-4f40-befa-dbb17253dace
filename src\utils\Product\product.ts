import { ITEM_NAMES, PRODUCTTYPES } from '@/constants';
import { BundleProductOption, CommerceProductBase, MembershipProductItem } from '@/models/Product';

export const getProductPricing = (fields: CommerceProductBase) => {
  const { GlobalPrice, GroupPricesEx: groupPrices, ProductStore: store, RenewalPrice } = fields;

  const currencyCode = (store?.fields?.StoreCurrency?.fields?.CurrencyCode?.value as string) || 'USD';
  const currencySymbol = (store?.fields?.StoreCurrency?.fields?.CurrencySymbol?.value as string) || '$';
  const memberPrice = groupPrices?.find(({ name }) => name === ITEM_NAMES.PRODUCT.CUSTOMER_GROUPS.INDIVIDUAL_MEMBERS);
  const studentMemberPrice = groupPrices?.find(
    ({ name }) => name === ITEM_NAMES.PRODUCT.CUSTOMER_GROUPS.STUDENT_MEMBERS,
  );

  return {
    currencyCode,
    currencySymbol,
    regularPrice: groupPrices?.some((gp) => gp?.name === ITEM_NAMES.PRODUCT.CUSTOMER_GROUPS.NON_MEMBER)
      ? groupPrices?.find((gp) => gp?.name === ITEM_NAMES.PRODUCT.CUSTOMER_GROUPS.NON_MEMBER).value
      : (GlobalPrice?.value ?? 0),
    memberPrice: memberPrice?.value ? +memberPrice.value : null,
    studentMemberPrice: studentMemberPrice?.value ? +studentMemberPrice.value : null,
    renewalPrice: RenewalPrice?.value ?? 0,
  };
};

export const getProductType = (fields: CommerceProductBase) => {
  const productTypeData = fields?.ProductType[0];
  return productTypeData?.name;
};

export const isElearningProduct = (fields: CommerceProductBase) => {
  const productTypeData = fields?.ProductType[0];
  return productTypeData?.name === PRODUCTTYPES.Elearning;
};

export const isMembershipProduct = (fields: CommerceProductBase) => {
  const productTypeData = fields?.ProductType[0];
  return productTypeData?.name === PRODUCTTYPES.Membership;
};

export const isChapterMembershipProduct = (fields: CommerceProductBase) => {
  const productTypeData = fields?.ProductType[0];
  return productTypeData?.name === PRODUCTTYPES.Chapter;
};

export const isStudentMembershipProduct = (fields: CommerceProductBase) => {
  const productTypeData = fields?.ProductType[0];
  if (productTypeData?.name !== PRODUCTTYPES.Membership) return false;
  const membershipProduct = fields as MembershipProductItem;
  return membershipProduct?.is_studentmembership?.value;
};

export const hasChapterOptionInMembershipProduct = (fields: CommerceProductBase) => {
  const productTypeData = fields?.ProductType[0];
  if (productTypeData?.name !== PRODUCTTYPES.Membership) return false;
  const options: BundleProductOption[] = JSON.parse(fields?.BundleProductOptions?.value);
  return options?.some((op) => op.title.toLowerCase().includes('chapter'));
};

const MembershipTypes = {
  Individual: 'Individual',
  Student: 'Student',
};

export const getMembershipType = (fields: CommerceProductBase) => {
  if (isStudentMembershipProduct(fields)) {
    return MembershipTypes.Student;
  }
  if (isMembershipProduct(fields)) {
    return MembershipTypes.Individual;
  }
  return null;
};

export const getDisplayPrice = (price: number) => {
  return Intl.NumberFormat('en-US').format(price);
};

export const getCountryPrices = (fields: CommerceProductBase) => {
  const countryPrices = fields?.CountryPricesEx || [];
  return countryPrices;
};

export const getCountryPriceByCode = (fields: CommerceProductBase, countryCode: string) => {
  const countryPrices = getCountryPrices(fields);
  const countryPrice = countryPrices.find((country) => country.country_code === countryCode);
  const nonMemberPrice = countryPrice?.prices.find((p) => p.customer_group === 'Non-Member');
  return nonMemberPrice;
};
