import React, { useMemo, useState } from 'react';
import { withEditorChromes } from '@sitecore-jss/sitecore-jss-react';
import {
  SelectContent,
  SelectIcon,
  SelectItem,
  SelectItemText,
  SelectPortal,
  SelectRoot,
  SelectScrollUpButton,
  SelectTrigger,
  SelectValue,
  SelectViewport,
} from '@pmi/catalyst-select';
import { ChevronDownIcon, ChevronUpIcon } from '@pmi/catalyst-icons';
import { withProductFields } from '@/hocs';
import { navigateToUrl } from '@/utils';
import { PATHS } from '@/constants';
import { LanguageSelectionProps } from './models';

const Component: React.FC<LanguageSelectionProps> = ({ fields }) => {
  const { OverviewLanguagesEx: languages = [], ExternalID: { value: sku } = { value: '' } } = fields;
  const currentLanguage = useMemo(() => {
    return languages.find((language) => (language?.value as string).toLowerCase() === (sku as string).toLowerCase());
  }, [languages, sku]);
  const [currentLanguageValue, setCurrentLanguageValue] = useState(currentLanguage?.value as string);

  const onValueChange = (newValue: string) => {
    setCurrentLanguageValue(newValue);
    navigateToUrl(`${PATHS.DCPDP_WITH_SKU}${newValue}`);
  };

  if (languages.length === 0 || !currentLanguageValue) {
    return null;
  }

  return (
    <div className="w-full pt-[--scale-24]">
      <h3 className="text-body-sm lg:text-body-md font-bold pb-[--scale-6]">Course Language</h3>
      <SelectRoot defaultValue={currentLanguageValue} onValueChange={onValueChange}>
        <SelectTrigger className="border-solid">
          <SelectValue />
          <SelectIcon>
            <ChevronDownIcon size="full" />
          </SelectIcon>
        </SelectTrigger>
        <SelectPortal>
          <SelectContent>
            <SelectScrollUpButton>
              <ChevronUpIcon />
            </SelectScrollUpButton>
            <SelectViewport>
              {languages.map(
                (language) =>
                  language.value && (
                    <SelectItem value={language.value} key={language.value}>
                      <SelectItemText>{language.name}</SelectItemText>
                    </SelectItem>
                  ),
              )}
            </SelectViewport>
          </SelectContent>
        </SelectPortal>
      </SelectRoot>
    </div>
  );
};

export default withEditorChromes(withProductFields(Component));
