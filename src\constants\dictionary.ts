export const PRODUCTDETAILPAGES_PHRASES = {
  HOUR: 'ProductDetailPages.Phrases.Hour',
  HOURS: 'ProductDetailPages.Phrases.Hours',
  CURRENT_VIEW: 'ProductDetailPages.Phrases.CurrentView',
};

export const PRODUCTDETAILPAGES_FIELDLABELS = {
  DIGITALPRODUCTBASE: {
    PDUS: 'ProductDetailPages.FieldLabels.DigitalProductBase.PDUs',
    LANGUAGES: 'ProductDetailPages.FieldLabels.DigitalProductBase.Languages',
    COURSEDURATION: 'ProductDetailPages.FieldLabels.DigitalProductBase.CourseDuration',
    ASSOCIATEDCERTIFICATIONS: 'ProductDetailPages.FieldLabels.DigitalProductBase.AssociatedCertifications',
    SUPPORTEDLANGUAGES: 'ProductDetailPages.FieldLabels.DigitalProductBase.SupportedLanguages',
  },
  COMMERCEPRODUCTBASE: {
    MEMBERSHIPPRICE: 'ProductDetailPages.FieldLabels.CommerceProductBase.MembershipPrice',
    FULLPRICE: 'ProductDetailPages.FieldLabels.CommerceProductBase.FullPrice',
    REGULARPRICE: 'ProductDetailPages.FieldLabels.CommerceProductBase.RegularPrice',
  },
  ELEARNING: {
    TOTALPDUS: 'ProductDetailPages.FieldLabels.eLearning.TotalPDUs',
    BUSINESSACUMEN: 'ProductDetailPages.FieldLabels.eLearning.BusinessAcumen',
    WAYSOFWORKING: 'ProductDetailPages.FieldLabels.eLearning.WaysOfWorking',
    POWERSKILLS: 'ProductDetailPages.FieldLabels.eLearning.PowerSkills',
  },
  PRICELOCKUP: {
    ALREADYMEMBEROFSPECIFICCHAPTERTEXT: 'ProductDetailPages.FieldLabels.PriceLockup.AlreadyMemberOfSpecificChapter',
    STUDENTMEMBERSHIPBILLINGPOLICY: 'ProductDetailPages.FieldLabels.PriceLockup.StudentMembershipBillingPolicy',
    SINGLEMEMBERSHIPCHAPTERINCLUSIONTEXT:
      'ProductDetailPages.FieldLabels.PriceLockup.SingleMembershipChapterInclusionText',
    INCLUDEDINMEMBERSHIPTEXT: 'ProductDetailPages.FieldLabels.PriceLockup.IncludedInMembershipText',
    AUTORENEWALTEXT: 'ProductDetailPages.FieldLabels.PriceLockup.AutoRenewalText',
    ALREADYPURCHASEDSUBSCRIPTIONTEXT: 'ProductDetailPages.SectionLabels.AlreadyPurchasedSubscriptionText',
    SUBSCRIPTIONINCLUDEDTEXT: 'ProductDetailPages.SectionLabels.SubscriptionIncludedText',
  },
  DONATION: {
    DONATIONAMOUNTPLACEHOLDERTEXT: 'ProductDetailPages.FieldLabels.Donation.DonationPlaceholderText',
    DONATIONTITLEFIELD: 'ProductDetailPages.FieldLabels.Donation.DonationTitleField',
  },
  CHAPTERMEMBERSHIP: {
    CONTACT: 'ProductDetailPages.FieldLabels.ChapterMembership.Contact',
    REGION: 'ProductDetailPages.FieldLabels.ChapterMembership.Region',
    STATUS: 'ProductDetailPages.FieldLabels.ChapterMembership.Status',
  },
};

export const PRODUCTDETAILPAGES_SECTIONLABELS = {
  OVERVIEW: 'ProductDetailPages.SectionLabels.Overview',
  OVERVIEWLANGUAGES: 'ProductDetailPages.SectionLabels.OverviewLanguages',
  OVERVIEWLANGUAGESTEXT: 'ProductDetailPages.SectionLabels.OverviewLanguagesText',
  RECOMMENDEDPRODUCTS: 'ProductDetailPages.SectionLabels.RecommendedProducts',
  MEMBERSHIPBENEFITS: 'ProductDetailPages.SectionLabels.MembershipBenefits',
  ABOUTCHAPTERS: 'ProductDetailPages.SectionLabels.AboutPmiChapters',
};

export const PRODUCTDETAILPAGES_BUTTONLABELS = {
  LEARNMORE: 'ProductDetailPages.ButtonLabels.LearnMore',
  ADDTOCARTBUTTONTEXT: 'ProductDetailPages.ButtonLabels.AddToCartButtonText',
  FREEPRODUCTADDTOCARTBUTTONTEXT: 'ProductDetailPages.ButtonLabels.FreeProductAddToCartText',
  GETACCESSBUTTON: 'ProductDetailPages.ButtonLabels.GetAccessButton',
  PRIORPUCHASEDSUBSCRIPTIONCTA: 'ProductDetailPages.ButtonLabels.AlreadyPurchasedSubcriptionCTA',
};

export const PRODUCTSEARCH_FACETLABELS = {
  PRODUCT_TYPE: 'ProductSearch.FacetLabels.ProductType',
  TOPICS: 'ProductSearch.FacetLabels.Topics',
  PDUS: 'ProductSearch.FacetLabels.Pdus',
  LEVEL: 'ProductSearch.FacetLabels.Level',
  LANGUAGE: 'ProductSearch.FacetLabels.Language',
};

export const PRODUCTSEARCH_SORTLABELS = {
  RELEVANCE: 'ProductSearch.SortLabels.Relevance',
  PRICE_ASCENDING: 'ProductSearch.SortLabels.PriceAscending',
  PRICE_DESCENDING: 'ProductSearch.SortLabels.PriceDescending',
};

export const PRODUCTDETAILPAGES_SEARCHRESULTSLABELS = {
  FEATURED: 'ProductSearch.SearchResultsLabels.Featured',
};
