import React from 'react';
import { withEditorChromes, Text } from '@sitecore-jss/sitecore-jss-react';
import { withProductFields } from '@/hocs';
import { Card } from '@pmi/catalyst-card';
import { Button } from '@pmi/catalyst-button';
import { ArrowRightIcon } from '@pmi/catalyst-icons';
import { useIsExperienceEditor } from '@/hooks';
import { useGetActiveMembershipsQuery, useGetCoursesQuery, useGetProductDetailsQuery } from '@pmi/www-shared/store';
import { getDisplayPrice } from '@/utils/Product/product';
import { MissingDataSource } from '@/components/common';
import { linkClickTracking } from '@pmi/www-shared/analytics';
import { CourseStatus } from '@/components/PriceLockup/models';
import { UserStatuses, useUserStatus } from '@pmi/www-shared/hooks';
import { cn } from '@pmi/catalyst-utils';
import { BasicProductInfoCardProps } from './models';

const Component: React.FC<BasicProductInfoCardProps> = ({ fields }) => {
  const { data: product } = useGetProductDetailsQuery(fields?.Sku?.value?.toString());
  const { data: userCourses } = useGetCoursesQuery();
  const isExperienceEditor = useIsExperienceEditor();
  const buttonContainerRef = React.useRef<HTMLDivElement>(null);
  const userStatus = useUserStatus();
  const isAuthenticated = userStatus === UserStatuses.LoggedIn;
  const { data: activeMemberships } = useGetActiveMembershipsQuery(null, { skip: !isAuthenticated });
  const isMember = !!activeMemberships?.hasActiveMembership;
  const completedCourse = userCourses?.find(
    (c) =>
      c.status === CourseStatus.Completed && c.courseId.toLocaleLowerCase() === product?.data?.sku.toLocaleLowerCase(),
  );

  // eslint-disable-next-line max-len
  const priceText = `${fields.PriceLabel?.value ?? 'Full Price:'} ${product?.data?.currencySymbol}${getDisplayPrice(product?.data?.regularPrice)}`;
  // eslint-disable-next-line max-len
  const memberPriceText = `${fields?.MemberPriceLabel?.value ?? 'Member Price:'} ${product?.data?.currencySymbol}${getDisplayPrice(product?.data?.memberPrice)}`;
  const formattedDate = completedCourse
    ? new Intl.DateTimeFormat(window?.navigator?.language ?? 'en-US', { dateStyle: 'short' }).format(
        new Date(completedCourse.completionDate),
      )
    : undefined;
  const completedOnText = `${fields?.CompletedLabel?.value ?? 'Completed on'} ${formattedDate}`;
  const showMemberPrice = product?.data?.memberPrice != null && product?.data?.memberPrice >= 0;

  const adobeRegion = {
    adoberegion: 'simple-product-card',
  };

  const handleClick = () => {
    linkClickTracking(`${product?.data?.title}-learn-more`, buttonContainerRef.current, product?.data?.productUrl);
  };

  if (isExperienceEditor && !fields.Sku) return <MissingDataSource />;

  if (!product) return null;

  return (
    <Card
      className="w-full lg:w-[--scale-240] group-data-[tab]:lg:w-full group-data-[tab]:lg:max-w-[--scale-240] text-[--text-primary] h-full flex flex-col justify-between"
      variant="ghost"
    >
      <div>
        <h2 className="font-medium text-header-2xs lg:text-header-xs ">
          <Text field={{ value: product?.data?.title }} encode={false} />
        </h2>
        {!!product?.data?.pdus && (
          <p className="my-[--scale-16] text-body-sm lg:text-body-md font-bold">
            {product.data?.pdus} {product.data?.pdus > 1 ? 'PDUs' : 'PDU'}
          </p>
        )}
        <p className="mb-[--scale-16] text-body-sm lg:text-body-md font-normal">
          {!fields?.ShortDescription?.value ? product?.data?.abstract : fields.ShortDescription.value}
        </p>
      </div>
      <div>
        {completedCourse ? (
          <p className="mb-[--scale-16] text-body-sm text-[--text-neutral-soft] lg:text-body-md font-normal ">
            {completedOnText}
          </p>
        ) : (
          <div>
            {(!isMember || !(product?.data?.memberPrice || product?.data?.memberPrice === 0)) && (
              <p
                className={cn(
                  'last:mb-[--scale-16] text-body-sm lg:text-body-md ',
                  showMemberPrice ? 'font-normal' : 'font-bold',
                )}
              >
                {priceText}
              </p>
            )}
            {showMemberPrice && (
              <p className="mb-[--scale-16] text-body-sm lg:text-body-md font-bold">{memberPriceText}</p>
            )}
          </div>
        )}
        <div className="basic-learn-more" ref={buttonContainerRef} {...adobeRegion}>
          <Button
            className="w-full lg:max-w-[200px] h-[--scale-48] lg:h-[--scale-32] text-[18px] lg:text-body-xs font-medium lg:px-[--scale-16] gap-[--scale-8] lg:gap-[--scale-4]"
            variant="outline"
            size="md"
            asChild
          >
            <a href={product?.data?.productUrl} onClick={handleClick}>
              <span className="pb-[1px] lg:pb-[2px]">{fields?.ButtonLabel?.value ?? 'Learn more'}</span>
              <div className="size-[--scale-24] lg:size-[--scale-16]">
                <ArrowRightIcon size="full" />
              </div>
            </a>
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default withEditorChromes(withProductFields(Component));
