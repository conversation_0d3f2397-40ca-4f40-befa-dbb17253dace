import React from 'react';
import { withEditorChromes, Text, Link } from '@sitecore-jss/sitecore-jss-react';
import { Button } from '@pmi/catalyst-button';
import { ArrowRightIcon } from '@pmi/catalyst-icons';
import { withClickTracking } from '@pmi/www-shared/analytics';
import { withProductFields } from '@/hocs';
import { OnlineCourseBreakdownHeaderProps } from './models';

const Component: React.FC<OnlineCourseBreakdownHeaderProps> = ({ fields }) => {
  const { Title, Description, ButtonLink } = fields;

  const adobeRegion = {
    adoberegion: 'pdp-pdu-breakdown',
  };

  return (
    <div className="max-w-screen-2xl mx-auto">
      <div
        {...adobeRegion}
        className="grid grid-cols-4 lg:grid-cols-12 mb-[--scale-32] lg:mb-[--scale-56] max-w-screen-xl mx-auto px-6 lg:px-4"
      >
        <h1 className="col-span-4 lg:col-span-5 font-medium text-header-md lg:text-header-lg">
          <Text field={Title} />
        </h1>
        <div className="text-body-md pt-[--scale-24] lg:pt-0 col-span-4 lg:!col-start-7 lg:col-span-6">
          <Text field={Description} />
          <Button
            className="w-full lg:w-auto mt-[--scale-24] lg:mt-[--scale-32]"
            variant="outline"
            size="md"
            onClick={withClickTracking<HTMLButtonElement>(ButtonLink.value.text, ButtonLink.value.href)}
          >
            <Link className="text-body-xs lg:text-body-md" field={ButtonLink} />
            <ArrowRightIcon size="md" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default withEditorChromes(withProductFields(Component));
