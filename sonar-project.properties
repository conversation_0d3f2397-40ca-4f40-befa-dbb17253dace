# Project Information
sonar.projectName=pmi-sitecore-spx-client-ci
sonar.projectKey=pmi-sitecore-spx-client-ci
sonar.sourceEncoding=utf-8

# Analysis Behavior
sonar.verbose=false

# Source and Test Configuration
sonar.sources=src/
sonar.language=ts
sonar.tests=src/

# Exclusions
sonar.exclusions=**/node_modules/**,**/*.test.ts,**/*.test.tsx
sonar.coverage.exclusions=**/{__mocks__,atoms,constants,containers,entrypoints,envs,handlers,hocs,layouts,models,services.store}/**

# Test Inclusions and Reports
sonar.test.inclusions=**/*.test.ts,**/*.test.tsx
sonar.eslint.reportPaths=output/eslint/eslint_report.json
#sonar.typescript.tsconfigPaths=tsconfig.sonar.json
sonar.javascript.lcov.reportPaths=output/jest/coverage/lcov.info
