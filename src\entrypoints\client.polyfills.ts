import { HtmlInputIds } from '@/layouts/constants';

const processorInputId = document.getElementById(HtmlInputIds.ProcessorInputId) as HTMLInputElement;
const processorVariables = processorInputId?.value ? JSON.parse(processorInputId.value) : {};
if (!window.process) {
  window.process = {} as any;
}
if (!window.process.env) {
  window.process.env = {} as any;
}
Object.keys(processorVariables).forEach((_) => {
  window.process.env[_] = processorVariables[_];
});
