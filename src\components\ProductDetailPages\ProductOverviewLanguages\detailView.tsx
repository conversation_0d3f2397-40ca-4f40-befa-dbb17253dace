import React from 'react';
import { withEditorChromes } from '@sitecore-jss/sitecore-jss-react';
import { useTranslation } from 'react-i18next';
import { withProductFields } from '@/hocs';
import { useIsExperienceEditor } from '@/hooks';
import { MissingDataSource } from '@/components/common';
import { PRODUCTDETAILPAGES_SECTIONLABELS } from '@/constants';
import { ProductOverviewLanguagesProps } from './models';
import { SelectedItemView, ItemView } from './itemView';

const adobeRegion = {
  adoberegion: 'pdp-languages',
};

const Component: React.FC<ProductOverviewLanguagesProps> = ({ fields }) => {
  const isExperienceEditor = useIsExperienceEditor();
  const { t } = useTranslation();
  const { OverviewLanguagesEx: languages, ExternalID: sku } = fields;

  const hasLanguages = languages?.length > 0;
  if (!hasLanguages) {
    return isExperienceEditor ? <MissingDataSource /> : null;
  }

  const sectionLabel = t(PRODUCTDETAILPAGES_SECTIONLABELS.OVERVIEWLANGUAGES, 'Languages');
  const sectionText = t(
    PRODUCTDETAILPAGES_SECTIONLABELS.OVERVIEWLANGUAGESTEXT,
    'Each link will take you to the product page in that language.',
  );

  return (
    <div {...adobeRegion} className="grid grid-cols-4 lg:grid-cols-12 pt-[--scale-40]">
      <h3 className="col-span-4 lg:col-span-5 font-medium text-header-xs lg:text-header-sm mobile:mb-[--scale-24]">
        {sectionLabel}
      </h3>
      <div className="col-span-4 lg:!col-start-7 lg:col-span-6 flex flex-col gap-[--scale-24] lg:gap-[--scale-24] w-full">
        <p className="font-normal text-body-sm lg:text-body-md">{sectionText}</p>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-[--scale-12] lg:gap-y-[--scale-8] lg:gap-x-[--scale-24] w-full">
          {languages.map((language) => (
            <div key={language.name} className="">
              {language?.value?.toString().toUpperCase() === sku?.value.toString().toUpperCase() ? (
                <SelectedItemView language={language} />
              ) : (
                <ItemView language={language} />
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default withEditorChromes(withProductFields(Component));
