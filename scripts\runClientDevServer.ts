import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import { BuildPipeline } from '@pmi/shared-component-factory';
import { clientDevServerConfigBuilder, clientDevConfigBuilder } from './configBuilders';
import { getClientAssetsForOtherAppsAsync } from '@pmi/shared-component-factory/devserver';
import { getAbsoluteOutputPath } from './configBuilders/getAbsoluteOutputPath';

dotenv.config({
  path: [
    process.env.USE_FAKEDATASERVER === 'true' ? './scripts/envs/.fakeDataServer.env' : '',
    './scripts/envs/.devserver.env',
    './scripts/envs/.env',
  ].filter((_) => !!_),
});
const runner = async () => {
  await getClientAssetsForOtherAppsAsync(getAbsoluteOutputPath());
  const buildPipeline = new BuildPipeline();
  await buildPipeline
    .if(() => !fs.existsSync(path.join(process.env.OUTPUT_PATH!, './clientCode/headless/client/libs')))
    .withClientLibs()
    .buildAsync();
  buildPipeline.runDevServerAsync({
    sharedComponentFactoryPath: '@/sharedComponentFactories/sharedComponentFactoryClient',
    clientConfigBuilder: clientDevConfigBuilder,
    clientDevServerConfigBuilder: clientDevServerConfigBuilder,
  });
};
runner();
