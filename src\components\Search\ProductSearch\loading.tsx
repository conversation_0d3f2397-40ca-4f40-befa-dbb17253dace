export const ProductSearchSkeleton: React.FC = () => {
  return (
    <div className="grid grid-cols-12 max-w-screen-xl mx-auto px-6 lg:px-4 pb-[--scale-80] pt-20">
      <div className="col-span-12">
        <div className="mb-[--scale-16] h-12 bg-gray-200 animate-pulse rounded" />
        <div className="flex gap-2 mb-6">
          <div className="h-6 w-24 bg-gray-200 animate-pulse rounded" />
          <div className="h-6 w-24 bg-gray-200 animate-pulse rounded" />
        </div>

        <div className="h-px bg-gray-200 my-6" />
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4 my-4">
          <div className="h-6 w-48 bg-gray-200 animate-pulse rounded" />
          <div className="h-6 w-32 bg-gray-200 animate-pulse rounded" />
        </div>
      </div>

      <div className="col-span-12 grid grid-cols-12 gap-[--scale-24]">
        <div className="mobile:hidden flex col-span-4 flex-col gap-[--scale-12]">
          {[1, 2, 3, 4].map((index) => (
            <div key={index} className="bg-white p-4 rounded shadow">
              <div className="h-6 w-32 bg-gray-200 animate-pulse rounded mb-4" />
              {[1, 2, 3].map((item) => (
                <div key={item} className="h-4 w-full bg-gray-200 animate-pulse rounded mb-2" />
              ))}
            </div>
          ))}
        </div>
        <div className="col-span-12 lg:col-span-8">
          {[1, 2, 3, 4, 5].map((index) => (
            <div key={index} className="mb-6 bg-white p-4 rounded shadow">
              <div className="h-6 w-3/4 bg-gray-200 animate-pulse rounded mb-2" />
              <div className="h-4 w-1/2 bg-gray-200 animate-pulse rounded mb-2" />
              <div className="h-4 w-full bg-gray-200 animate-pulse rounded" />
            </div>
          ))}
          <div className="flex flex-col sm:flex-row justify-between items-center mt-4 gap-4">
            <div className="flex gap-2">
              {[1, 2, 3, 4, 5].map((index) => (
                <div key={index} className="h-8 w-8 bg-gray-200 animate-pulse rounded" />
              ))}
            </div>
            <div className="h-8 w-32 bg-gray-200 animate-pulse rounded" />
          </div>
        </div>
      </div>
    </div>
  );
};
