import path from 'path';
import fs from 'fs';

const getTsConfigLocation = () => path.join(__dirname, '../../tsconfig.json');

const getResolve = () => {
  const tsConfig = JSON.parse(fs.readFileSync(getTsConfigLocation()).toString());
  const basePath = path.resolve(__dirname, '../../', tsConfig.compilerOptions.baseUrl);
  return {
    extensions: ['.ts', '.tsx', '.js', '.json'],
    alias: { '@': [basePath] },
  };
};

export { getTsConfigLocation, getResolve };
