import {
  CAPMOutline,
  PMIACPOutline,
  PMICPOutline,
  PMIPBAOutline,
  PMIRMPOutline,
  PMOCCOutline,
  PMOCPOutline,
  PMPOutline,
  PfMPOutline,
  PgMPOutline,
} from '@pmi/catalyst-wordmarks';
import React from 'react';

export interface WordmarkByNameProps {
  className?: string;
  size: 'full' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | null | undefined;
  color:
    | 'accent'
    | 'inherit'
    | 'original'
    | 'primary'
    | 'secondary'
    | 'info'
    | 'danger'
    | 'warning'
    | 'success'
    | 'white'
    | 'inverted'
    | 'neutral-soft'
    | 'neutral-dark'
    | null
    | undefined;
  name?: string;
}

export const WordmarkByName: React.FC<WordmarkByNameProps> = (props) => {
  switch (props.name) {
    case 'CAPM':
      return <CAPMOutline color={props.color} size={props.size} className={props.className} />;
    case 'PMI-ACP':
      return <PMIACPOutline color={props.color} size={props.size} className={props.className} />;
    case 'PMI-CP':
      return <PMICPOutline color={props.color} size={props.size} className={props.className} />;
    case 'PMI-PBA':
      return <PMIPBAOutline color={props.color} size={props.size} className={props.className} />;
    case 'PMI-RMP':
      return <PMIRMPOutline color={props.color} size={props.size} className={props.className} />;
    case 'PMO-CC':
      return <PMOCCOutline color={props.color} size={props.size} className={props.className} />;
    case 'PMO-CP':
      return <PMOCPOutline color={props.color} size={props.size} className={props.className} />;
    case 'PMP':
      return <PMPOutline color={props.color} size={props.size} className={props.className} />;
    case 'PfMP':
      return <PfMPOutline color={props.color} size={props.size} className={props.className} />;
    case 'PgMP':
      return <PgMPOutline color={props.color} size={props.size} className={props.className} />;
    default:
      return null;
  }
};
