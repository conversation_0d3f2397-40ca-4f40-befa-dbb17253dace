import { useEffect } from 'react';
import LogRocket from 'logrocket';
import { useIsExperienceEditor } from '@pmi/www-shared/hooks';
import { useGetUserQuery } from '@pmi/www-shared/store';
import useSpxHeadlessSettings from '../Sitecore/useSpxHeadlessSettings';

export const useLogRocketTracking = () => {
  const isExperienceEditor = useIsExperienceEditor();
  const settings = useSpxHeadlessSettings();
  const { data: user, isLoading: isUserLoading } = useGetUserQuery();

  useEffect(() => {
    if (!isExperienceEditor && settings.EnableLogRocketAnalytics && settings.LogRocketProductKey && !isUserLoading) {
      LogRocket.init(settings.LogRocketProductKey, {
        network: {
          isEnabled: !!settings.EnableLoggingNetworkCalls,
        },
        shouldParseXHRBlob: true,
      });
      if (!isUserLoading && user?.personId > 1) {
        LogRocket.identify(user.personId.toString());
      }
    }
  }, [settings, isExperienceEditor, isUserLoading, user?.personId]);
};
