export interface HeaderRow {
  actionLink?: ActionLink;
  description: string;
  price: string;
  sku: string;
  title: string;
  isSelected: boolean;
}

interface ActionLink {
  text: string;
  url: string;
}

export interface BodyRow {
  columnValues: ColumntValueProps[];
  description: string;
  title: string;
}

export interface RowViewProps {
  data: string;
  leftColumnCondition: boolean;
  rightColumnCondition: boolean;
}

export interface ColumntValueProps {
  value: string | boolean;
}
