import React from 'react';
import { Helmet } from 'react-helmet';
import { Placeholder, withEditorChromes } from '@sitecore-jss/sitecore-jss-react';
import { useRouteFields } from '@/hooks';
import { CommerceProductBase } from '@/models';
import { ProductDetailPageContainerProps } from './models';

const Component: React.FC<ProductDetailPageContainerProps> = ({ rendering }) => {
  const { HtmlTitle: htmlTitle } = useRouteFields<CommerceProductBase>();
  return (
    <>
      <Helmet title={htmlTitle?.value as string} />
      <div id="spx-website-pdp">
        <div>
          <Placeholder name="spx-pdp-header" rendering={rendering} />
        </div>
        <div>
          <Placeholder name="spx-pdp-content" rendering={rendering} />
        </div>
      </div>
    </>
  );
};

export const ProductDetailPageContainer = withEditorChromes(Component);
