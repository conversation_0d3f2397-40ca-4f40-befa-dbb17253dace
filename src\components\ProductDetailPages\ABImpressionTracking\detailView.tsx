import { useRouteFields } from '@/hooks';
import { useABImpressionTracking } from '@/hooks/analytics';
import { CommerceProductBase } from '@/models';
import { useEffect, useRef } from 'react';

/**
 * Component that tracks A/B test impressions on product page loads
 * This component should be placed on product detail pages to automatically
 * track A/B test impressions after the page loads
 */
const ABImpressionTracking: React.FC = () => {
  const product = useRouteFields<CommerceProductBase>();
  const { trackABImpression } = useABImpressionTracking();
  const hasTracked = useRef(false);

  useEffect(() => {
    // Only track once per page load and only if we have product data
    if (product && !hasTracked.current) {
      // Use a small delay to ensure page load is complete
      const timeoutId = setTimeout(() => {
        trackABImpression(product);
        hasTracked.current = true;
      }, 100);

      return () => {
        clearTimeout(timeoutId);
      };
    }

    // Return undefined when no cleanup is needed
    return undefined;
  }, [product, trackABImpression]);

  // Reset tracking flag when product changes (navigation to different product)
  useEffect(() => {
    hasTracked.current = false;
  }, [product?.ExternalID?.value]);

  // This component doesn't render anything visible
  return null;
};

export default ABImpressionTracking;
