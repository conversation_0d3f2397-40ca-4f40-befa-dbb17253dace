import React from 'react';
import { withEditorChromes, Text, RichText } from '@sitecore-jss/sitecore-jss-react';
import { useScreenSize } from '@pmi/www-shared/hooks';
import { useGetProductsQuery } from '@/store';
import { useRouteFields } from '@/hooks';
import { CommerceProductBase } from '@/models';
import * as Desktop from './components/desktop';
import * as Mobile from './components/mobile';
import { HeaderView } from './components/headerView';
import { ProductComparisonProps } from './models';
import { useBodyData, useHeaderData } from './hooks';

const Component: React.FC<ProductComparisonProps> = ({ fields, params }) => {
  const { Title: title, SubText: subText } = fields;
  const isMobile = useScreenSize();
  const currentProduct = useRouteFields<CommerceProductBase>();
  const currentProductStoreCode = currentProduct?.ProductStore?.fields?.ExternalID?.value as string;
  const currentProductSku = currentProduct?.ExternalID?.value as string;
  const productComparisonSkus = fields?.productsWithBenefits.map((product) => product.productSku);
  const { data: productsData, isFetching: isProductsDataFetching } = useGetProductsQuery({
    skus: productComparisonSkus,
    storeCode: currentProductStoreCode,
  });

  const products = isProductsDataFetching ? [] : productsData?.data || [];

  const highlightedColumn = fields?.productsWithBenefits.findIndex(
    (p) => p.productSku.toLowerCase() === params.highlightedProductSku?.toLowerCase() || p.highlightCurrentItem,
  );

  const headerData = useHeaderData(fields, products, currentProductSku);
  const bodyData = useBodyData(fields);

  return (
    <>
      <Text tag="h1" className="text-header-lg pb-20" field={title} />
      <div className="mobile:overflow-x-auto mobile:mobile-scrollbar mobile:min-w-60 mobile:pb-4">
        <table className="w-full table-auto border-separate border-spacing-0 [&_td]:mobile:w-1/2 [&_td]:mobile:min-w-60">
          <thead>
            <tr>
              {!isMobile && <th>&nbsp;</th>}
              <HeaderView headers={headerData} highlightedColumn={highlightedColumn} />
            </tr>
          </thead>
          <tbody>
            {isMobile ? (
              <Mobile.BodyView rows={bodyData} highlightedColumn={highlightedColumn} />
            ) : (
              <Desktop.BodyView rows={bodyData} highlightedColumn={highlightedColumn} />
            )}
          </tbody>
          {!isMobile && (
            <tfoot>
              <tr>
                <td>&nbsp;</td>
                <td className="pt-6 text-body-sm" colSpan={2}>
                  <RichText tag="p" field={subText} />
                </td>
              </tr>
            </tfoot>
          )}
        </table>
      </div>
      {isMobile && <RichText tag="p" className="mt-6 text-body-sm" field={subText} />}
    </>
  );
};

export default withEditorChromes(Component);
