import React from 'react';
import { Button, ButtonStylized } from '@pmi/catalyst-button';
import { cn } from '@pmi/catalyst-utils';
import { MobileStickyFooter } from '@/components/ui/MobileStickyFooter';
import type { ButtonComponentProps, VariantType } from './models';

const isStylizedVariant = (variant: VariantType) => {
  return ['primary', 'secondary', 'tertiary'].includes(variant);
};

export const ButtonComponent: React.FC<ButtonComponentProps> = ({
  variant,
  buttonText,
  onClick,
  classNames = '',
  danger = false,
  icon,
  iconPosition = 'left',
  size,
  disabled,
  ref,
  mobileStickyFooter = false,
}) => {
  const customStyles = cn('max-md:flex-col md:flex-row', classNames);

  let ButtonComp: React.ElementType;
  let buttonProps: ButtonComponentProps = {
    className: customStyles,
    onClick,
    variant,
    size,
    disabled,
    ref,
  };
  if (isStylizedVariant(variant)) {
    ButtonComp = ButtonStylized;
  } else {
    ButtonComp = Button;
    buttonProps = {
      danger,
      ...buttonProps,
    };
  }

  return (
    <MobileStickyFooter isSticky={mobileStickyFooter}>
      <ButtonComp {...buttonProps}>
        <>
          {icon && iconPosition === 'left' && { icon }}
          {buttonText}
          {icon && iconPosition === 'right' && { icon }}
        </>
      </ButtonComp>
    </MobileStickyFooter>
  );
};
