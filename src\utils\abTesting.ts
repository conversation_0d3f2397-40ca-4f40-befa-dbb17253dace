import { ABTestConfig } from '@/models';

/**
 * <PERSON><PERSON> key for storing A/B test variants
 */
const AB_TEST_COOKIE_KEY = 'pmi_ab_tests';

/**
 * Interface for storing A/B test variants in cookies
 */
interface ABTestVariants {
  [testName: string]: string;
}

/**
 * Get all A/B test variants from cookies
 */
export const getABTestVariants = (): ABTestVariants => {
  if (typeof document === 'undefined') {
    return {};
  }

  try {
    const cookieValue = getCookie(AB_TEST_COOKIE_KEY);
    return cookieValue ? JSON.parse(cookieValue) : {};
  } catch (error) {
    console.warn('Failed to parse A/B test variants from cookie:', error);
    return {};
  }
};

/**
 * Set A/B test variants in cookies
 */
export const setABTestVariants = (variants: ABTestVariants): void => {
  if (typeof document === 'undefined') {
    return;
  }

  try {
    const cookieValue = JSON.stringify(variants);
    setCookie(AB_TEST_COOKIE_KEY, cookieValue, 30); // 30 days expiration
  } catch (error) {
    console.warn('Failed to set A/B test variants in cookie:', error);
  }
};

/**
 * Get a specific cookie value
 */
const getCookie = (name: string): string | null => {
  const nameEQ = `${name}=`;
  const ca = document.cookie.split(';');
  
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }
  
  return null;
};

/**
 * Set a cookie value
 */
const setCookie = (name: string, value: string, days: number): void => {
  const expires = new Date();
  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
  document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`;
};

/**
 * Randomly select a variant from the available variants
 */
export const selectRandomVariant = (variants: string[]): string => {
  if (variants.length === 0) {
    throw new Error('No variants available for selection');
  }
  
  const randomIndex = Math.floor(Math.random() * variants.length);
  return variants[randomIndex];
};

/**
 * Get or assign a variant for a specific A/B test
 */
export const getOrAssignVariant = (testConfig: ABTestConfig): string | null => {
  if (!testConfig.enabled || testConfig.variants.length === 0) {
    return null;
  }

  const currentVariants = getABTestVariants();
  
  // Check if user already has a variant assigned for this test
  if (currentVariants[testConfig.testName]) {
    // Verify the assigned variant is still valid
    if (testConfig.variants.includes(currentVariants[testConfig.testName])) {
      return currentVariants[testConfig.testName];
    }
  }

  // Assign a new variant
  const selectedVariant = selectRandomVariant(testConfig.variants);
  
  // Store the new variant
  const updatedVariants = {
    ...currentVariants,
    [testConfig.testName]: selectedVariant,
  };
  
  setABTestVariants(updatedVariants);
  
  return selectedVariant;
};

/**
 * Generate the full variant name following the naming convention
 * Format: testName:variant (e.g., "spx:el106:v01:verA")
 */
export const generateVariantName = (testName: string, variant: string): string => {
  return `${testName}:${variant}`;
};

/**
 * Check if a product SKU is eligible for the A/B test
 */
export const isProductEligibleForTest = (productSku: string, testConfig: ABTestConfig): boolean => {
  if (!testConfig.enabled) {
    return false;
  }

  // If no specific SKUs are configured, the test applies to all products
  if (!testConfig.productSkus || testConfig.productSkus.length === 0) {
    return true;
  }

  // Check if the product SKU is in the configured list
  return testConfig.productSkus.includes(productSku);
};

/**
 * Parse test name to extract components
 * Expected format: "team:sku:version" (e.g., "spx:el106:v01")
 */
export const parseTestName = (testName: string): { team: string; sku: string; version: string } | null => {
  const parts = testName.split(':');
  
  if (parts.length !== 3) {
    console.warn(`Invalid test name format: ${testName}. Expected format: "team:sku:version"`);
    return null;
  }

  return {
    team: parts[0],
    sku: parts[1],
    version: parts[2],
  };
};

/**
 * Generate test name following the naming convention
 * Format: "team:sku:version" (e.g., "spx:el106:v01")
 */
export const generateTestName = (team: string, sku: string, version: string): string => {
  return `${team}:${sku}:${version}`;
};
