import { Field, TextField } from '@sitecore-jss/sitecore-jss-react';
import * as Jss from '@/models/Jss';

export interface SpxProductCardData extends Jss.BaseDataSourceItem {
  Sku: TextField;
  ShortDescription: TextField;
  buttonText: TextField;
  showPrices: Field<boolean>;
  showCTA: Field<boolean>;
  membershipTextHighlightColor: MembershipTextBgColorItem;
}

export interface MembershipTextBgColorItem extends Jss.BaseDataSourceItem {
  name: string;
}

export interface SpxProductCardDataProps extends Jss.Rendering<SpxProductCardData> {}
