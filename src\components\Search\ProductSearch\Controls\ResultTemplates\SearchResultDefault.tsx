import { usePriceLockupTexts } from '@/components/PriceLockup/usePriceLockupTexts';
import { ButtonComponent } from '@/components/ui';
import { PRODUCTDETAILPAGES_BUTTONLABELS, PRODUCTDETAILPAGES_SEARCHRESULTSLABELS } from '@/constants';
import { usePageRouting } from '@/hooks';
import { getDisplayPrice, isNullOrUndefined } from '@/utils';
import { buildInteractiveResult } from '@coveo/headless';
import { Badge } from '@pmi/catalyst-badge';
import { Link } from '@pmi/catalyst-link';
import { cn } from '@pmi/catalyst-utils';
import { searchClickTracking } from '@pmi/www-shared/analytics';
import { CoveoContext } from '@pmi/www-shared/components';
import { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { SearchResultProps } from '../../models';
import { getProductPricing } from '../../utils';

export const SearchResultDefault: React.FC<SearchResultProps> = ({ result, storeResolvingCountry, membership }) => {
  const { searchEngine } = useContext(CoveoContext);
  const interactiveResult = buildInteractiveResult(searchEngine, {
    options: {
      result,
    },
  });
  const { regularPriceText, fullPriceText, membershipPriceText } = usePriceLockupTexts();
  const { t } = useTranslation();
  const { redirectToPageUrl } = usePageRouting();
  const pricing = getProductPricing(result, storeResolvingCountry);

  const isMember = !!membership?.hasActiveMembership;
  const pageUrl = result.clickUri;
  const buttonText = t(PRODUCTDETAILPAGES_BUTTONLABELS.LEARNMORE, 'Learn More');

  const { raw, isTopResult } = result;
  const {
    haslayout: hasLayout,
    producttitle: productTitle,
    abstract,
    productpriceunittype: currencySymbol,
    producttypetitle: productType,
  } = raw;

  const dimRegularPrice = isMember && !isNullOrUndefined(pricing.memberPrice);

  // consider expanding search model to get code from the product item when there will be need to specify multiple codes
  const currencyCode = currencySymbol === '$' ? 'USD' : null;

  const handleLearnMoreClick = () => {
    searchClickTracking(raw.producttitle, 'coveo-learn-more', result.clickUri);
    interactiveResult.select();
    redirectToPageUrl(pageUrl);
  };

  const mainDivStyles = cn('w-full flex flex-col gap-4 px-6', {
    'theme-pmi-dark color-[--text-secondary] bg-[--surface-secondary] rounded-[--rounded-xs] py-6': isTopResult,
  });

  return (
    <div className={mainDivStyles} {...{ adoberegion: 'coveo-result-row' }}>
      {/* Badge */}
      {isTopResult && (
        <div>
          <Badge key="featured" color="off-black" variant="soft" size="sm">
            {t(PRODUCTDETAILPAGES_SEARCHRESULTSLABELS.FEATURED, 'Featured')}
          </Badge>
        </div>
      )}
      {/* Title */}
      <div className="flex flex-wrap items-start gap-2 w-full">
        <div className="min-w-0" {...{ adoberegion: 'coveo-title' }}>
          {hasLayout === '1' ? (
            <Link
              className="text-[--text-primary] text-[18px] sm:text-[22px] font-medium leading-[22px] sm:leading-[26px] underline inline-block w-full"
              href={result.clickUri}
              onClick={() => {
                interactiveResult.select();
                searchClickTracking(raw.producttitle, 'coveo-title', result.clickUri);
              }}
            >
              <div dangerouslySetInnerHTML={{ __html: productTitle }} />
            </Link>
          ) : (
            <span className="text-[--text-primary] text-[18px] sm:text-[22px] font-medium leading-[22px] sm:leading-[26px] underline inline-block w-full">
              <div dangerouslySetInnerHTML={{ __html: productTitle }} />
            </span>
          )}
        </div>
      </div>
      {/* Description */}
      {!isNullOrUndefined(abstract) && (
        <div
          className="text-[--text-secondary] text-base sm:text-base text-sm leading-5 sm:leading-5"
          dangerouslySetInnerHTML={{ __html: abstract }}
        />
      )}
      {/* Pricing */}
      {productType !== 'Donation' && (
        <div className="border-[length:--border-xs] border-transparent grid grid-cols-4 lg:grid-cols-6 w-full rounded-none lg:gap-x-[--scale-24]">
          <div className="col-span-6 lg:col-span-4 flex flex-col">
            <div className="grid grid-cols-6 gap-[--scale-24]">
              {!isNullOrUndefined(pricing.memberPrice) && (
                <div className="col-span-2">
                  <div className="col-span-2 text-[--text-primary]">
                    <h2 className="text-body-md font-bold">{membershipPriceText}</h2>
                    <span className="text-body-md font-bold">
                      {currencyCode} {currencySymbol}
                      {getDisplayPrice(pricing.memberPrice)}
                    </span>
                  </div>
                </div>
              )}
              {!isNullOrUndefined(pricing.nonMemberPrice) && (
                <div className="col-span-2">
                  <div
                    className={cn(
                      'col-span-2',
                      dimRegularPrice ? 'text-[--text-neutral-softer]' : 'text-[--text-primary]',
                    )}
                  >
                    <h2 className="text-body-md font-normal">
                      {isNullOrUndefined(pricing.memberPrice) ? regularPriceText : fullPriceText}
                    </h2>
                    <span className="text-body-md font-normal">
                      {currencyCode} {currencySymbol}
                      {getDisplayPrice(pricing.nonMemberPrice)}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
          <div
            className="col-span-4 lg:col-start-5 lg:col-span-2 flex pt-[--scale-24] lg:pt-[--scale-4] justify-end"
            {...{ adoberegion: 'coveo-learn-more' }}
          >
            <ButtonComponent
              classNames="lg:whitespace-nowrap"
              variant="solid"
              buttonText={buttonText}
              onClick={handleLearnMoreClick}
              danger={false}
              size="sm"
            />
          </div>
        </div>
      )}
    </div>
  );
};
