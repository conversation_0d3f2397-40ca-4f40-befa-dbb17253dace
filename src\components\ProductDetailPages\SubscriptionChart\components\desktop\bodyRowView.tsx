/* eslint-disable react/no-array-index-key */
import React from 'react';
import { CircleCheckFilledIcon } from '@pmi/catalyst-icons';
import { cn } from '@pmi/catalyst-utils';
import { BodyRow, RowViewProps, ColumntValueProps } from '../models';

export const BodyRowView: React.FC<RowViewProps> = ({ data, leftColumnCondition, rightColumnCondition }) => {
  const bodyRows = JSON.parse(data as string);

  if (!bodyRows) {
    return null;
  }

  const drawRowsBorder = cn({
    '[tr:not(:last-child)_&:nth-child(2)]:border-x-2': leftColumnCondition,
    '[tr:not(:last-child)_&:nth-child(3)]:border-x-2': rightColumnCondition,
  });

  const drawLastRowBorder = cn(
    {
      '[tr:last-child_td:nth-child(2)_&]:border-x-2': leftColumnCondition,
      '[tr:last-child_td:nth-child(3)_&]:border-x-2': rightColumnCondition,
    },
    {
      '[tr:last-child_td:nth-child(2)_&]:border-b-2': leftColumnCondition,
      '[tr:last-child_td:nth-child(3)_&]:border-b-2': rightColumnCondition,
    },
    {
      '[tr:last-child_td:nth-child(2)_&]:rounded-b-3xl': leftColumnCondition,
      '[tr:last-child_td:nth-child(3)_&]:rounded-b-3xl': rightColumnCondition,
    },
    'border-[--colors-off-black-800]',
  );

  return (
    <>
      {bodyRows.map((bodyRow: BodyRow, bodyRowIndex: number) => (
        <tr key={`d-row-${bodyRowIndex}`} className="odd:bg-[--surface-secondary]">
          <td className="text-left font-display font-normal bg-[--colors-white] pr-4">
            <div className="text-body-md">{bodyRow.title}</div>
            {bodyRow.description && <div className="text-base text-[--text-secondary]">{bodyRow.description}</div>}
          </td>
          {bodyRow.columnValues.map((columnValue: ColumntValueProps, columnIndex: number) => (
            <td key={`d-td-${columnIndex}`} className={`p-0 border-[--colors-off-black-800] ${drawRowsBorder}`}>
              <div className={`py-6 flex justify-center w-ful text-header-xs font-medium ${drawLastRowBorder}`}>
                {typeof columnValue.value === 'boolean'
                  ? columnValue.value && <CircleCheckFilledIcon size="lg" />
                  : columnValue.value}
              </div>
            </td>
          ))}
        </tr>
      ))}
    </>
  );
};
