import { useTranslation } from 'react-i18next';
import { ButtonComponent, LoadingOverlay } from '@/components/ui';
import type { ButtonComponentProps } from '@/components/ui/Button';
import { PRODUCTDETAILPAGES_BUTTONLABELS } from '@/constants';
import { useLoadingDebounce, useRouteFields, useSignalRService } from '@/hooks';
import { useActivateFreeProductMutation } from '@/store';
import { CommerceProductBase } from '@/models';
import { useEffect } from 'react';
import { GetAccessButtonProps } from './models';

export const GetAccessButton: React.FC<GetAccessButtonProps> = ({ params }) => {
  const contextItem = useRouteFields<CommerceProductBase>();
  const { t } = useTranslation();
  const signalRConnection = useSignalRService();
  const [activateFreeProductMutation, { isLoading, isError }] = useActivateFreeProductMutation();
  const [loading, setLoading] = useLoadingDebounce(false, 500);
  const sku = contextItem?.ExternalID;

  const variant = params?.variant as ButtonComponentProps['variant'];
  const iconPosition = params?.iconPosition as ButtonComponentProps['iconPosition'];
  const size = params?.size as ButtonComponentProps['size'];

  const buttonText = t(PRODUCTDETAILPAGES_BUTTONLABELS.GETACCESSBUTTON) ?? 'Get Access';

  const handleClick = () => {
    setLoading(true);
    signalRConnection.signalRConnection.on('userFulfillmentResult', (message) => {
      // TODO: Added console.log for testing purpose. Should be updated when the feature will be finished.
      console.log('Message from SignalR: ', message);
      signalRConnection.signalRConnection.stop();
      setLoading(false);
    });
    signalRConnection.signalRConnection.start().catch((ex) => {
      console.log('SignalR connection start block error:', ex);
      setLoading(false);
    });

    try {
      activateFreeProductMutation({ sku: sku?.value?.toString(), qty: 1 });
    } catch (ex) {
      signalRConnection.signalRConnection.stop();
      setLoading(false);
      console.error('SignalR connection stopped because of error: ', ex);
    }
  };

  useEffect(() => {
    if (!isLoading && isError) {
      setLoading(false);
      signalRConnection.signalRConnection.stop();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isError, isLoading, setLoading]);

  return (
    <>
      <ButtonComponent
        variant={variant}
        buttonText={buttonText}
        onClick={handleClick}
        iconPosition={iconPosition}
        danger={false}
        size={size}
      />
      <LoadingOverlay
        open={loading}
        description={t('ProductDetailPages.OverlayLabels.ProductActivation', 'Unlocking new insights.')}
      />
    </>
  );
};
