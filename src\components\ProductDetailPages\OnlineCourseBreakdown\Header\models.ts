import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ield } from '@sitecore-jss/sitecore-jss-react';
import * as Jss from '@/models/Jss';

export interface OnlineCourseBreakdownHeaderDataSource extends Jss.BaseDataSourceItem {
  Title?: TextField;
  Description?: TextField;
  ButtonLink?: LinkField;
}

export interface OnlineCourseBreakdownHeaderProps extends Jss.Rendering<OnlineCourseBreakdownHeaderDataSource> {}
