import { MissingDataSource } from '@/components/common';
import { PRODUCTDETAILPAGES_FIELDLABELS } from '@/constants';
import { withProductFields } from '@/hocs';
import { useIsExperienceEditor } from '@/hooks';
import { withEditorChromes } from '@sitecore-jss/sitecore-jss-react';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ChapterContactInformationDetailProps } from './models';

const replaceHyphens = (text: string) => {
  if (!text) return text;
  // Replace regular hyphen with non-breaking hyphen Unicode character
  return text.replace(/-/g, '‑'); // Unicode character U+2011 (Non-Breaking Hyphen)
};

const Component: React.FC<ChapterContactInformationDetailProps> = ({ fields }) => {
  const { t } = useTranslation();
  const { chapter_email: email, chapter_phone: phoneNumber } = fields;

  const isExperienceEditor = useIsExperienceEditor();
  const hasContactInformation = !!email?.value || !!phoneNumber?.value;

  if (!hasContactInformation) return isExperienceEditor ? <MissingDataSource /> : null;

  const { CONTACT } = PRODUCTDETAILPAGES_FIELDLABELS.CHAPTERMEMBERSHIP;

  const contactLabel = t(CONTACT, 'Contact');

  const modifiedEmailField = email && {
    ...email,
    value: replaceHyphens(email.value as string),
  };

  return (
    <div className="w-full pt-[--scale-24]">
      <div className="grid grid-cols-2 lg:grid-cols-3 gap-[--scale-24]">
        <div className="flex flex-col gap-[--scale-4]">
          {contactLabel && <p className="text-body-sm lg:text-body-md font-bold">{contactLabel}</p>}
          {modifiedEmailField?.value && (
            <p className="text-body-sm lg:text-body-md font-normal">{modifiedEmailField.value}</p>
          )}
          {phoneNumber?.value && <p className="text-body-sm lg:text-body-md font-normal">{phoneNumber.value}</p>}
        </div>
      </div>
    </div>
  );
};

export default withEditorChromes(withProductFields(Component));
