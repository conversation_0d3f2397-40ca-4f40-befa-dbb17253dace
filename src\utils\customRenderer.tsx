import { FC, PropsWithChildren, ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';

const Providers: FC<PropsWithChildren> = ({ children }) => {
  return (
    <Provider
      store={configureStore({
        reducer: {
          api: () => ({}),
          errors: () => ({}),
        },
      })}
    >
      {children}
    </Provider>
  );
};

const customRender = (ui: ReactElement, options?: Omit<RenderOptions, 'wrapper'>) =>
  render(ui, { wrapper: Providers, ...options });

// re-export everything
export * from '@testing-library/react';

// override render method
export { customRender as render };
