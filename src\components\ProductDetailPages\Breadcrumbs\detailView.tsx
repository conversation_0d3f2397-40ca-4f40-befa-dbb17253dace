import { Link, Text, <PERSON>Field, <PERSON>Field, withEditorChromes } from '@sitecore-jss/sitecore-jss-react';
import {
  BreadcrumbRoot,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbSeparator,
  BreadcrumbPage,
  BreadcrumbEllipsis,
} from '@pmi/catalyst-breadcrumb';
import { CommerceProductBase } from '@/models';
import { useRouteFields } from '@/hooks';
import { BreadcrumbProps } from './models';

const Component: React.FC<BreadcrumbProps> = ({ fields }) => {
  const contextItem = useRouteFields<CommerceProductBase>();
  const itemName = contextItem.HtmlTitle as TextField;
  const level3Hierarchy = contextItem?.level_3_hierarchy?.[0]?.name;
  const searchPageLinkValue = fields?.SearchPageLink?.value;

  let link: LinkField = null;
  if (searchPageLinkValue && level3Hierarchy) {
    link = {
      value: {
        ...searchPageLinkValue,
        anchor: searchPageLinkValue.anchor?.replace('{level3Hierarchy}', level3Hierarchy),
        text: level3Hierarchy,
      },
    };
  }

  const adobeRegion = {
    adoberegion: 'pdp-bread-crumbs',
  };

  return (
    <BreadcrumbRoot {...adobeRegion}>
      <BreadcrumbList className="justify-start">
        {link && (
          <>
            <BreadcrumbItem>
              <Link field={link} />
            </BreadcrumbItem>
            <BreadcrumbSeparator>/</BreadcrumbSeparator>
          </>
        )}
        <BreadcrumbItem>
          <BreadcrumbPage>
            <span className="mobile:hidden lg:inline-flex">
              <Text field={itemName} encode={false} />
            </span>{' '}
            <BreadcrumbEllipsis className="lg:hidden">...</BreadcrumbEllipsis>
          </BreadcrumbPage>
        </BreadcrumbItem>
        <BreadcrumbItem />
      </BreadcrumbList>
    </BreadcrumbRoot>
  );
};

export default withEditorChromes(Component);
