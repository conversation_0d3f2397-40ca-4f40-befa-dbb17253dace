import { useTranslation } from 'react-i18next';
import { PRODUCTDE<PERSON>ILPAGES_BUTTONLABELS, PRODUCT<PERSON><PERSON>ILPAGES_FIELDLABELS } from '@/constants';

export const usePriceLockupTexts = () => {
  const { t } = useTranslation();

  return {
    membershipPriceText: t(PRODUCT<PERSON><PERSON>ILPAGES_FIELDLABELS.COMMERCEPRODUCTBASE.MEMBERSHIPPRICE, 'Member Price'),
    regularPriceText: t(PRODUCTDETAILPAGES_FIELDLABELS.COMMERCEPRODUCTBASE.REGULARPRICE, 'Price'),
    fullPriceText: t(PRODUCTDETAILPAGES_FIELDLABELS.COMMERCEPRODUCTBASE.FULLPRICE, 'Full Price'),
    addToCartButtonText: t(PRODUCTDETAILPAGES_BUTTONLABELS.ADDTOCARTBUTTONTEXT, 'Add To Cart'),
    freeProductAddToCartText: t(PRODUCTDETAILPAGES_BUTTONLABELS.FREEPRODUCTADDTOCARTBUTTONTEXT, 'Get Access'),
    donationPlaceholderText: t(
      PRODUCTDETAILPAGES_FIELDLABELS.DONATION.DONATIONAMOUNTPLACEHOLDERTEXT,
      'Enter donation amount',
    ),
    alreadyPurchasedSubsriptionCTA: t(PRODUCTDETAILPAGES_BUTTONLABELS.PRIORPUCHASEDSUBSCRIPTIONCTA, 'view in Account'),
    alreadyPurchasedSubscriptionText: t(
      PRODUCTDETAILPAGES_FIELDLABELS.PRICELOCKUP.ALREADYPURCHASEDSUBSCRIPTIONTEXT,
      'You are currently subscribed to this product',
    ),
    subscriptionIncludedText: t(
      PRODUCTDETAILPAGES_FIELDLABELS.PRICELOCKUP.SUBSCRIPTIONINCLUDEDTEXT,
      'This subscription is included in your membership',
    ),
    includedInMembershipText: t(
      PRODUCTDETAILPAGES_FIELDLABELS.PRICELOCKUP.INCLUDEDINMEMBERSHIPTEXT,
      'Included in Membership',
    ),
    autoRenewalText: t(PRODUCTDETAILPAGES_FIELDLABELS.PRICELOCKUP.AUTORENEWALTEXT, 'Auto-renews '),
    donationTitleField: t(PRODUCTDETAILPAGES_FIELDLABELS.DONATION.DONATIONTITLEFIELD, 'Donation Amount'),
  };
};
