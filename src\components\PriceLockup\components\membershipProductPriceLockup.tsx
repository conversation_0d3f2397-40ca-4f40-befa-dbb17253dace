import { ButtonComponent } from '@/components/ui';
import {
  getDisplayPrice,
  navigateToCartPageWithMembership,
  navigateToCartPageWithSku,
  navigateToUrl,
  stringIsNullOrEmpty,
} from '@/utils';
import { cn } from '@pmi/catalyst-utils';
import { linkClickTracking } from '@pmi/www-shared/analytics';
import { Text } from '@sitecore-jss/sitecore-jss-react';
import { useRef } from 'react';
import { usePriceLookupContext } from '../context';
import { usePriceLockupLogic } from '../hooks/usePriceLockupLogic';
import { MembershipProductPriceLockupProps } from '../models';
import { AlreadyInCart } from './alreadyInCart';

const MembershipProductPriceLockup = ({ isSticky }: MembershipProductPriceLockupProps) => {
  const { isInCart, isLoading } = usePriceLookupContext();
  const buttonContainerRef = useRef<HTMLDivElement>(null);
  const {
    membershipInfo,
    productInfo,
    buttonInfo,
    fields,
    settings,
    membershipTypeRedirect,
    product,
    isLoading: isPriceLockupLogicLoading,
  } = usePriceLockupLogic();
  const { canRenew, isStudentMember, expirationDate, isMember } = membershipInfo;
  const {
    currencyCode,
    currencySymbol,
    priceToShow,
    isDeleted,
    hasChapterBundleOption,
    isSingleMembershipEnabledCountry,
    productMembershipType,
  } = productInfo;
  const { text: buttonText } = buttonInfo;

  const disabledCTA = (isDeleted === true && !canRenew && !isMember) || isLoading || isInCart;
  const formattedPriceToShow = getDisplayPrice(priceToShow);

  const handleClick = () => {
    if (isMember && !canRenew) {
      linkClickTracking(buttonText, buttonContainerRef.current, `${settings?.MypmiUrl}/account`);
      navigateToUrl(`${settings?.MypmiUrl}/membership`);
    } else if (membershipTypeRedirect) {
      navigateToCartPageWithMembership(membershipTypeRedirect, window?.location?.pathname);
    } else {
      navigateToCartPageWithSku(product?.ExternalID?.value, window?.location?.pathname);
    }
  };

  const canRenewElement = () =>
    canRenew ? (
      <p className="text-body-xs font-normal text-[--text-neutral-soft] mt-[--scale-4]">
        <Text field={fields.membershipExpiringText} encode={false} /> {expirationDate}.
      </p>
    ) : (
      <p className="text-body-xs font-normal text-[--text-neutral-soft] mt-[--scale-4]">
        <Text field={fields.autoRenewText} encode={false} />
      </p>
    );

  const isStudentSection = isStudentMember ? (
    <div className="col-span-6 lg:col-span-4 flex flex-col">
      <p className="text-body-sm lg:text-body-md">
        <Text field={fields.youAreAlreadyStudentMemberText} /> {expirationDate}.
      </p>
    </div>
  ) : null;

  if (isPriceLockupLogicLoading) return null;

  return (
    <>
      <div className="col-span-6 lg:col-span-4 flex flex-col">
        {isMember && !canRenew && !isStudentMember ? (
          <p className="text-body-sm lg:text-body-md">
            <Text field={fields.youAreAlreadyMemberText} encode={false} /> {expirationDate}.
          </p>
        ) : (
          <>
            <h2 className="text-body-sm lg:text-body-md font-bold">{productMembershipType} membership</h2>
            <span className="text-body-sm lg:text-body-md font-bold">
              {!stringIsNullOrEmpty(currencyCode) && `${currencyCode} `}
              {currencySymbol}
              {formattedPriceToShow} / year
            </span>

            {!isSticky && !isStudentMember && canRenewElement()}
            {!isSticky && isStudentSection}

            {hasChapterBundleOption && isSingleMembershipEnabledCountry && (
              <p className={cn('text-body-xs mt-[--scale-8]', { hidden: isSticky })}>
                <Text field={fields.chapterSelectionText} encode={false} />
              </p>
            )}
          </>
        )}
        <AlreadyInCart display={isInCart} />
      </div>

      <div
        className="col-span-4 lg:col-start-5 lg:col-span-2 flex justify-end pt-[--scale-24] lg:pt-0"
        ref={buttonContainerRef}
      >
        <ButtonComponent
          classNames="w-full lg:w-max"
          disabled={disabledCTA}
          variant="solid"
          buttonText={buttonText}
          onClick={handleClick}
          danger={false}
          size="sm"
          mobileStickyFooter
        />
      </div>
    </>
  );
};

export default MembershipProductPriceLockup;
