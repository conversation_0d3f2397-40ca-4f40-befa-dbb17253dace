import { ButtonComponent, ButtonComponentProps } from '@/components/ui';
import { PRODUCTDETAILPAGES_FIELDLABELS } from '@/constants';
import { getDisplayPrice, navigateToUrl, stringIsNullOrEmpty } from '@/utils';
import { Button } from '@pmi/catalyst-button';
import {
  DialogClose,
  DialogContent,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  DialogTrigger,
} from '@pmi/catalyst-dialog';
import { CloseIcon } from '@pmi/catalyst-icons';
import { cn } from '@pmi/catalyst-utils';
import { linkClickTracking } from '@pmi/www-shared/analytics';
import { Text } from '@sitecore-jss/sitecore-jss-react';
import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { usePriceLookupContext } from '../context';
import { usePriceLockupLogic } from '../hooks/usePriceLockupLogic';
import { PriceLockupProps } from '../models';
import { AlreadyInCart } from './alreadyInCart';

const StudentMemberPriceLockup: React.FC<PriceLockupProps> = (props) => {
  const buttonContainerRef = useRef<HTMLDivElement>(null);
  const { isInCart, isLoading } = usePriceLookupContext();
  const {
    membershipInfo,
    productInfo,
    buttonInfo,
    settings,
    isLoading: isPriceLockupLogicLoading,
  } = usePriceLockupLogic();
  const { t } = useTranslation();

  const { canRenew, isStudentMember, expirationDate } = membershipInfo;
  const {
    currencyCode,
    currencySymbol,
    priceToShow,
    hasChapterBundleOption,
    productName,
    isDeleted,
    isSingleMembershipEnabledCountry,
  } = productInfo;
  const { text: buttonText } = buttonInfo;
  const formattedPriceToShow = getDisplayPrice(priceToShow);

  const singlemembershipChapterText = t(
    PRODUCTDETAILPAGES_FIELDLABELS.PRICELOCKUP.SINGLEMEMBERSHIPCHAPTERINCLUSIONTEXT,
    'A chapter selection is included in the membership price. You will be prompted to make a selection in the cart.',
  );

  const studentMembershipBillingPolicyText = t(
    PRODUCTDETAILPAGES_FIELDLABELS.PRICELOCKUP.STUDENTMEMBERSHIPBILLINGPOLICY,
    'Must verify student status every year.',
  );

  const buttonRenderingProps: ButtonComponentProps = {
    variant: 'solid',
    buttonText: '',
    onClick: () => {},
    size: 'sm',
    classNames: 'w-full lg:w-max',
    danger: false,
  };

  const handleClick = () => {
    linkClickTracking(
      props.fields.viewInAccountButtonText?.value,
      buttonContainerRef.current,
      `${settings?.MypmiUrl}/account`,
    );
    navigateToUrl(`${settings?.MypmiUrl}/account`);
  };

  const disabledCTA = isDeleted || isLoading || isInCart;

  if (isPriceLockupLogicLoading) return null;

  return (
    <>
      <div className="col-span-4">
        <div className="col-span-2">
          {!props.isSticky && <h2 className="text-body-sm lg:text-body-md font-bold">{productName}</h2>}
          <span className="text-body-sm lg:text-body-md  font-bold">
            {!stringIsNullOrEmpty(currencyCode) && `${currencyCode} `}
            {currencySymbol}
            {formattedPriceToShow}/ year
          </span>
          {isStudentMember ? (
            <div className="col-span-6 lg:col-span-4 flex flex-col">
              <p className="text-body-sm lg:text-body-md">
                <Text field={props.fields.youAreAlreadyStudentMemberText} /> {expirationDate}.
              </p>
            </div>
          ) : null}
          <p
            className={cn('text-body-xs font-normal text-[--text-neutral-soft] mt-[--scale-4]', {
              hidden: props.isSticky,
            })}
          >
            {studentMembershipBillingPolicyText}
          </p>
        </div>
        {hasChapterBundleOption && isSingleMembershipEnabledCountry ? (
          <p className={cn('text-body-xs mt-[--scale-8]', { hidden: props.isSticky })}>{singlemembershipChapterText}</p>
        ) : null}
        <AlreadyInCart display={isInCart} />
      </div>
      {isStudentMember && !canRenew ? (
        <div className="col-span-4 lg:col-span-2 flex justify-end pt-[--scale-24] lg:pt-0" ref={buttonContainerRef}>
          <ButtonComponent
            classNames={buttonRenderingProps.classNames}
            variant={buttonRenderingProps.variant}
            buttonText={props.fields.viewInAccountButtonText?.value}
            onClick={handleClick}
            danger={buttonRenderingProps.danger}
            size={buttonRenderingProps.size}
            mobileStickyFooter
          />
        </div>
      ) : (
        <DialogRoot>
          <DialogTrigger asChild>
            <div className="col-span-4 lg:col-span-2 flex justify-end pt-[--scale-24] lg:pt-0">
              <ButtonComponent
                classNames={buttonRenderingProps.classNames}
                variant={buttonRenderingProps.variant}
                buttonText={buttonText}
                disabled={disabledCTA}
                onClick={() => {}}
                danger={buttonRenderingProps.danger}
                size={buttonRenderingProps.size}
                mobileStickyFooter
              />
            </div>
          </DialogTrigger>
          <DialogPortal>
            <DialogOverlay className="z-[1000]" />
            <DialogContent
              className={cn(
                'fixed top-[50%] left-[50%] max-h-[85vh] w-[90vw]',
                'max-w-full translate-x-[-50%] translate-y-[-50%]',
                'rounded-[--rounded-md] bg-[--surface-primary] p-[--scale-12] focus:outline-none',
                'flex flex-col z-[1020]', // Changed to flex-col
              )}
            >
              <div className="flex justify-end mb-2">
                <DialogClose asChild>
                  <Button variant="ghost" size="sm" className="p-[4px] rounded-[--rounded-xs]">
                    <CloseIcon size="lg" />
                    <span className="sr-only">Close</span>
                  </Button>
                </DialogClose>
              </div>
              <iframe
                title="sheerid-student"
                style={{ width: 'calc(100%)', height: 'calc(80vh)' }}
                src={settings.SheerIdUrl}
              />
            </DialogContent>
          </DialogPortal>
        </DialogRoot>
      )}
    </>
  );
};

export default StudentMemberPriceLockup;
