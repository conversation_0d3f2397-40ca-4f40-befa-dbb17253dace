import { CommerceProductBase } from '@/models';
import { getProductPricing } from '@/utils';
import { generateVariantName, getOrAssignVariant, isProductEligibleForTest } from '@/utils/abTesting';
import { ABTestEventNames, emitEvent, EventTypes } from '@pmi/analytics-layer';
import useSpxHeadlessSettings from '../Sitecore/useSpxHeadlessSettings';

/**
 * Interface for A/B impression event data
 */
interface ABImpressionEventData {
  testName: string;
  experienceName: string;
  productSku: string;
  productName: string;
  regularPrice: number;
  memberPrice?: number;
  studentMemberPrice?: number;
  currencyCode: string;
  currencySymbol: string;
  variant: string;
}

/**
 * Hook for tracking A/B test impressions on product pages
 */
export const useABImpressionTracking = () => {
  const settings = useSpxHeadlessSettings();

  /**
   * Track A/B impression event for a product
   */
  const trackABImpression = (product: CommerceProductBase) => {
    try {
      // Check if A/B testing is configured
      const abTestConfig = settings?.ABTestConfig;
      if (!abTestConfig || !abTestConfig.enabled) {
        return;
      }

      // Get product SKU
      const productSku = product?.ExternalID?.value as string;
      if (!productSku) {
        console.warn('Product SKU not available for A/B impression tracking');
        return;
      }

      // Check if this product is eligible for the A/B test
      if (!isProductEligibleForTest(productSku, abTestConfig)) {
        return;
      }

      // Get or assign variant for this user
      const variant = getOrAssignVariant(abTestConfig);
      if (!variant) {
        console.warn('Failed to assign A/B test variant');
        return;
      }

      // Generate the experience name (full variant name)
      const experienceName = generateVariantName(abTestConfig.testName, variant);

      // Get product pricing information
      const pricingInfo = getProductPricing(product);

      // Get member pricing
      const { memberPrice } = pricingInfo;
      const { studentMemberPrice } = pricingInfo;

      // Prepare event data
      const eventData: ABImpressionEventData = {
        testName: abTestConfig.testName,
        experienceName,
        productSku,
        productName: (product?.Name?.value as string) || '',
        regularPrice: pricingInfo.regularPrice,
        memberPrice: memberPrice || undefined,
        studentMemberPrice: studentMemberPrice || undefined,
        currencyCode: pricingInfo.currencyCode,
        currencySymbol: pricingInfo.currencySymbol,
        variant,
      };

      // Emit the A/B impression event
      emitEvent({
        eventType: EventTypes.ABTest,
        eventName: ABTestEventNames.ABImpression,
        eventData,
        ab: {
          testName: abTestConfig.testName,
          experienceName,
        },
      });

      console.log('A/B impression tracked:', {
        testName: abTestConfig.testName,
        experienceName,
        productSku,
        variant,
      });
    } catch (error) {
      console.error('Failed to track A/B impression:', error);
    }
  };

  /**
   * Track A/B click event (for future use)
   */
  const trackABClick = (product: CommerceProductBase, clickTarget: string) => {
    try {
      const abTestConfig = settings?.ABTestConfig;
      if (!abTestConfig || !abTestConfig.enabled) {
        return;
      }

      const productSku = product?.ExternalID?.value as string;
      if (!productSku || !isProductEligibleForTest(productSku, abTestConfig)) {
        return;
      }

      const variant = getOrAssignVariant(abTestConfig);
      if (!variant) {
        return;
      }

      const experienceName = generateVariantName(abTestConfig.testName, variant);

      emitEvent({
        eventType: EventTypes.ABTest,
        eventName: ABTestEventNames.ABClick,
        eventData: {
          testName: abTestConfig.testName,
          experienceName,
          productSku,
          clickTarget,
          variant,
        },
        ab: {
          testName: abTestConfig.testName,
          experienceName,
        },
      });
    } catch (error) {
      console.error('Failed to track A/B click:', error);
    }
  };

  return {
    trackABImpression,
    trackABClick,
  };
};
