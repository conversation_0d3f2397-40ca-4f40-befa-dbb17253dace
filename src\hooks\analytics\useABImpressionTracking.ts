import { CommerceProductBase } from '@/models';
import { generateVariantName, getOrAssignVariant, isProductEligibleForTest } from '@/utils/abTesting';
import { ABTestEventNames, emitEvent, EventTypes } from '@pmi/analytics-layer';
import { UserStatuses, useUserStatus } from '@pmi/www-shared/hooks';
import { useGetActiveMembershipsQuery } from '@pmi/www-shared/store';
import useSpxHeadlessSettings from '../Sitecore/useSpxHeadlessSettings';

/**
 * Interface for A/B impression event data matching Adobe Analytics format
 */
interface ABImpressionEventData {
  ab: {
    testName: string;
    experienceName: string;
    status: 'member' | 'nonmember' | 'unknown';
  };
}

/**
 * Hook for tracking A/B test impressions on product pages
 */
export const useABImpressionTracking = () => {
  const settings = useSpxHeadlessSettings();
  const userStatus = useUserStatus();
  const isAuthenticated = userStatus === UserStatuses.LoggedIn;
  const { data: activeMemberships } = useGetActiveMembershipsQuery(null, { skip: !isAuthenticated });

  /**
   * Determine user membership status
   */
  const getMembershipStatus = (): 'member' | 'nonmember' | 'unknown' => {
    if (!isAuthenticated) {
      return 'unknown';
    }

    if (activeMemberships?.hasActiveMembership) {
      return 'member';
    }

    return 'nonmember';
  };

  /**
   * Track A/B impression event for a product
   */
  const trackABImpression = (product: CommerceProductBase) => {
    try {
      // Check if A/B testing is configured
      const abTestConfig = settings?.ABTestConfig;
      if (!abTestConfig || !abTestConfig.enabled) {
        return;
      }

      // Get product SKU
      const productSku = product?.ExternalID?.value as string;
      if (!productSku) {
        console.warn('Product SKU not available for A/B impression tracking');
        return;
      }

      // Check if this product is eligible for the A/B test
      if (!isProductEligibleForTest(productSku, abTestConfig)) {
        return;
      }

      // Get or assign variant for this user
      const variant = getOrAssignVariant(abTestConfig);
      if (!variant) {
        console.warn('Failed to assign A/B test variant');
        return;
      }

      // Generate the experience name (full variant name)
      const experienceName = generateVariantName(abTestConfig.testName, variant);

      // Get membership status
      const membershipStatus = getMembershipStatus();

      // Prepare event data in Adobe Analytics format
      const eventData: ABImpressionEventData = {
        ab: {
          testName: abTestConfig.testName,
          experienceName,
          status: membershipStatus,
        },
      };

      // Emit the A/B impression event
      emitEvent({
        eventType: EventTypes.ABTest,
        eventName: ABTestEventNames.ABImpression,
        eventData,
      });

      console.log('A/B impression tracked:', {
        testName: abTestConfig.testName,
        experienceName,
        productSku,
        variant,
      });
    } catch (error) {
      console.error('Failed to track A/B impression:', error);
    }
  };

  /**
   * Track A/B click event (for future use)
   */
  const trackABClick = (product: CommerceProductBase, clickTarget: string) => {
    try {
      const abTestConfig = settings?.ABTestConfig;
      if (!abTestConfig || !abTestConfig.enabled) {
        return;
      }

      const productSku = product?.ExternalID?.value as string;
      if (!productSku || !isProductEligibleForTest(productSku, abTestConfig)) {
        return;
      }

      const variant = getOrAssignVariant(abTestConfig);
      if (!variant) {
        return;
      }

      const experienceName = generateVariantName(abTestConfig.testName, variant);

      emitEvent({
        eventType: EventTypes.ABTest,
        eventName: ABTestEventNames.ABClick,
        eventData: {
          testName: abTestConfig.testName,
          experienceName,
          productSku,
          clickTarget,
          variant,
        },
        ab: {
          testName: abTestConfig.testName,
          experienceName,
        },
      });
    } catch (error) {
      console.error('Failed to track A/B click:', error);
    }
  };

  return {
    trackABImpression,
    trackABClick,
  };
};
