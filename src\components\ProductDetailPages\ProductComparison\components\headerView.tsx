import React from 'react';
import { ChevronRightIcon } from '@pmi/catalyst-icons';
import { Skeleton } from '@pmi/catalyst-skeleton';
import { cn } from '@pmi/catalyst-utils';
import { Header, HeaderViewProps } from '../models';

export const HeaderView: React.FC<HeaderViewProps> = ({ headers, highlightedColumn }) => {
  if (!headers || headers.length === 0) {
    return null;
  }

  return (
    <>
      {headers.map((header: Header, index) => {
        const shouldHighlight = index === highlightedColumn;
        const thClass = cn('align-top text-center font-display font-normal p-6 text-[--text-primary]', {
          'border-x-2 border-t-2 rounded-t-3xl border-[--colors-off-black-dark]': shouldHighlight,
        });
        return (
          <th key={header.key} className={thClass}>
            <h5 className="font-medium text-header-2xs md:text-header-xs">
              {header?.title ? header.title : <Skeleton className="block">Loading</Skeleton>}
            </h5>
            <div className="text-body-sm md:text-body-md py-2">
              {!header?.hidePrice && (header?.price ? header.price : <Skeleton className="block">Loading</Skeleton>)}
            </div>
            <div className="text-body-xs text-[--text-secondary] pb-2">{header?.description}</div>
            {header?.actionLink?.text && (
              <div className="font-medium text-body-xs pb-4">
                {header?.actionLink?.url ? (
                  <a href={header?.actionLink?.url}>
                    {header?.actionLink?.text}
                    <ChevronRightIcon size="xs" />
                  </a>
                ) : (
                  <Skeleton className="block">Loading</Skeleton>
                )}
              </div>
            )}
          </th>
        );
      })}
    </>
  );
};
