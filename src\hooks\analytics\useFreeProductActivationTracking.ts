import { ButtonInteractionEventNames, emitEvent, EventTypes, ProductViewLoadEventNames } from '@pmi/analytics-layer';

export const useFreeProductActivationTracking = () => {
  const trackFreeProductGetAccessButtonClickEvent = () => {
    emitEvent({
      eventType: EventTypes.ButtonInteraction,
      eventName: ButtonInteractionEventNames.GetAccessClick,
      eventData: {},
    });
  };

  const trackLaunchFreeCourseClickEvent = () => {
    emitEvent({
      eventType: EventTypes.ButtonInteraction,
      eventName: ButtonInteractionEventNames.LaunchFreeCourse,
      eventData: {},
    });
  };

  const trackFreeProductActivationSuccessEvent = (productCategory: string, productName: string, popupTime: string) => {
    emitEvent({
      eventType: EventTypes.ProductViewLoad,
      eventName: ProductViewLoadEventNames.FreeAccessSuccess,
      eventData: {
        freeaccess: {
          productName,
          productCat: productCategory,
          popupTime,
        },
      },
    });
  };

  return {
    trackFreeProductGetAccessButtonClickEvent,
    trackLaunchFreeCourseClickEvent,
    trackFreeProductActivationSuccessEvent,
  };
};
