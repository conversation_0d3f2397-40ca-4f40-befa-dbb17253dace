import React from 'react';
import { withEditorChromes, Text } from '@sitecore-jss/sitecore-jss-react';
import { withProductFields } from '@/hocs';
import { useIsExperienceEditor } from '@/hooks';
import { MissingDataSource } from '@/components/common';
import { ProductTitleProps } from './models';

const Component: React.FC<ProductTitleProps> = ({ fields }) => {
  const { Title: title } = fields;

  const isExperienceEditor = useIsExperienceEditor();
  if (!title?.value) {
    return isExperienceEditor ? <MissingDataSource /> : null;
  }

  return (
    <h1 className="lg:text-header-md text-header-sm font-medium">
      <Text field={title} encode={false} />
    </h1>
  );
};

export default withEditorChromes(withProductFields(Component));
