import dotenv from 'dotenv';
import { BuildPipeline } from '@pmi/shared-component-factory';
import { clientConfigBuilder, serverConfigBuilder } from './configBuilders';

dotenv.config({
  path: [
    process.env.USE_FAKEDATASERVER === 'true' ? './scripts/envs/.fakeDataServer.env' : '',
    './scripts/envs/.evelopment.env',
    './scripts/envs/.env',
  ].filter((_) => !!_),
});

new BuildPipeline()
  .if(process.env.BUILD_SERVER == 'true')
  .withServer({
    sharedComponentFactoryPath: '@/sharedComponentFactories/sharedComponentFactoryServer',
    serverConfigBuilder: serverConfigBuilder,
  })
  .if(process.env.BUILD_CLIENT == 'true')
  .withClient({
    sharedComponentFactoryPath: '@/sharedComponentFactories/sharedComponentFactoryClient',
    clientConfigBuilder: clientConfigBuilder,
  })
  .buildAsync()
  .then(
    () => console.info('The build was successful'),
    (_) => {
      console.error(`Errors while running the build: ${_}`);
      process.exitCode = 1;
    },
  );
