import { PipelineArgs } from '@pmi/shared-component-factory/clientRuntime';
import { buildReduxApiAsync } from './buildReduxApi';
import { getProcessorVarsAsync } from './getProcessorVars';

export const mappedHandlers: { [key: string]: typeof buildReduxApiAsync } = {
  BuildReduxApi: buildReduxApiAsync,
  GetProcessorVars: getProcessorVarsAsync,
};

export const mainHandlerAsync = async (args: PipelineArgs) => {
  const handler = mappedHandlers[args.methodName];
  await handler?.(args);
};
