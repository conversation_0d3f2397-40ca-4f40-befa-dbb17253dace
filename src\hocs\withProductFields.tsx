import type { FC } from 'react';
import * as Jss from '@/models/Jss';
import { useRouteFields } from '@/hooks';

// This HOC checks if the component has a datasource (fields is not null)
// and if it has none, falls back to the page item as a datacoure
export const withProductFields = <T extends Jss.Rendering<Jss.BaseDataSourceItem>>(Component: FC<T>) => {
  return (props: T) => {
    const pageFields = useRouteFields<Jss.BaseDataSourceItem>();
    const fields = props.fields || pageFields;

    return <Component {...{ ...props, fields }} />;
  };
};
