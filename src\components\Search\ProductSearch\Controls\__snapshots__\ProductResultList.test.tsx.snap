// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ProductResultList should render result list 1`] = `
<div
  class="w-full"
>
  <div
    adoberegion="coveo-result-row"
    class="w-full flex flex-col gap-4 px-6"
  >
    <div
      class="flex flex-wrap items-start gap-2 w-full"
    >
      <div
        adoberegion="coveo-title"
        class="min-w-0"
      >
        <span
          class="text-[--text-primary] text-[18px] sm:text-[22px] font-medium leading-[22px] sm:leading-[26px] underline inline-block w-full"
        >
          <div>
            PMI Educational Foundation (PMIEF)
          </div>
        </span>
      </div>
    </div>
    <div
      class="border-[length:--border-xs] border-transparent grid grid-cols-4 lg:grid-cols-6 w-full rounded-none lg:gap-x-[--scale-24]"
    >
      <div
        class="col-span-6 lg:col-span-4 flex flex-col"
      >
        <div
          class="grid grid-cols-6 gap-[--scale-24]"
        />
      </div>
      <div
        adoberegion="coveo-learn-more"
        class="col-span-4 lg:col-start-5 lg:col-span-2 flex pt-[--scale-24] lg:pt-[--scale-4] justify-end"
      >
        <button
          class="inline-flex justify-center items-center group transition active:scale-95 rounded-[--rounded-full] focus-visible:outline-[length:--border-sm] focus-visible:outline-offset-2 focus-visible:outline data-[disabled]:cursor-not-allowed data-[disabled]:pointer-events-none data-[disabled]:opacity-[--opacity-disabled] bg-[--fill-off-black-darkest] text-[--text-inverted] hover:bg-[--fill-off-black-dark] focus-visible:outline-[--border-off-black-dark] data-[disabled]:bg-[--fill-off-black-darkest] h-[--scale-40] px-[--scale-16] gap-[--scale-6] max-md:flex-col md:flex-row lg:whitespace-nowrap"
          data-size="sm"
        >
          Learn More
        </button>
      </div>
    </div>
  </div>
  <div
    class="px-6"
  >
    <div
      class="rounded-[--rounded-none] bg-[--components-separator-fill-neutral] h-[--scale-1] w-full mt-8"
      data-orientation="horizontal"
      role="separator"
    />
  </div>
</div>
`;
