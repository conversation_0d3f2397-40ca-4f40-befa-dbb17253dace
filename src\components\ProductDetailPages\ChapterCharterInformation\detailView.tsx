import React, { ReactNode } from 'react';
import { withEditorChromes, Text } from '@sitecore-jss/sitecore-jss-react';
import { useTranslation } from 'react-i18next';
import { withProductFields } from '@/hocs';
import { useIsExperienceEditor } from '@/hooks';
import { PRODUCTDETAILPAGES_FIELDLABELS } from '@/constants';
import { MissingDataSource } from '@/components/common';
import { ChapterCharterInformationDetailProps } from './models';

const Detail = ({ label, value }: { label: string | ReactNode; value: string | number | ReactNode }) => {
  return (
    <div className="flex flex-col gap-[--scale-4]">
      <p className="text-body-sm lg:text-body-md font-bold">{label}</p>
      <p className="text-body-sm lg:text-body-md font-normal">{value}</p>
    </div>
  );
};

const Component: React.FC<ChapterCharterInformationDetailProps> = ({ fields }) => {
  const { t } = useTranslation();
  const { region, charter_status: status, charter_year: fullYear } = fields;
  const regionName = region?.fields?.Label;
  const statusName = status?.fields?.Label;

  const isExperienceEditor = useIsExperienceEditor();
  const hasCharterInformation = !!regionName && !!statusName;

  if (!hasCharterInformation) return isExperienceEditor ? <MissingDataSource /> : null;

  const year = new Date(fullYear?.value).getFullYear();
  const { REGION, STATUS } = PRODUCTDETAILPAGES_FIELDLABELS.CHAPTERMEMBERSHIP;

  const regionLabel = t(REGION, 'Region');
  const statusLabel = t(STATUS, 'Status');
  const statusText = statusName.value === 'Chartered' && year ? `${statusName.value} since ${year}` : statusName.value;

  return (
    <div className="pt-[--scale-24] w-full">
      <div className="grid grid-cols-2 gap-[--scale-24]">
        <Detail label={regionLabel} value={<Text field={regionName} />} />
        <Detail label={statusLabel} value={statusText} />
      </div>
    </div>
  );
};

export default withEditorChromes(withProductFields(Component));
