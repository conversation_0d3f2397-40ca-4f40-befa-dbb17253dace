import { useCallback, useEffect, useRef, useState } from 'react';

export const useLoadingDebounce = (initialValue: boolean = false, minDelay: number = 500) => {
  const [loading, setLoadingDirect] = useState(initialValue);
  const startTimestamp = useRef<number | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const setLoading = useCallback(
    (value: boolean) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (value) {
        setLoadingDirect(true);
        startTimestamp.current = Date.now();
      } else {
        const elapsed = startTimestamp.current ? Date.now() - startTimestamp.current : 0;
        const remainingTime = Math.max(minDelay - elapsed, 0);

        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = setTimeout(() => {
          setLoadingDirect(false);
          startTimestamp.current = null;
        }, remainingTime);
      }
    },
    [minDelay],
  );

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return [loading, setLoading] as const;
};
