import React, { useRef } from 'react';
import { Placeholder, withEditorChromes } from '@sitecore-jss/sitecore-jss-react';
import { cn } from '@pmi/catalyst-utils';
import { withProductFields } from '@/hocs';
import { isChapterMembershipProduct, isMembershipProduct } from '@/utils';
import { PreloadImage, PreloadBgImage, ResponsiveImage } from '@pmi/www-shared/components';
import { desktopBgCssVariable, mobileBgCssVariable, resolveImageSrc } from '@pmi/www-shared/utils';
import { ProductHeroProps } from './models';

const Component: React.FC<ProductHeroProps> = ({ fields, rendering }) => {
  const bgRef = useRef<HTMLDivElement>(null);
  const isDarkTheme = fields?.UseDarkThemeForHero?.value;
  const isMembership = isMembershipProduct(fields);
  const isChapter = isChapterMembershipProduct(fields);
  const backgroundImage = resolveImageSrc(fields?.HeroBackgroundImage);
  const backgroundImageMobile = resolveImageSrc(fields?.HeroBackgroundImageMobile);

  const bgStyles = cn(
    'overflow-hidden',
    'text-[--text-primary]',
    'bg-cover bg-no-repeat',
    'mobile:bg-[image:--mobile-image] mobile:bg-top',
    'lg:bg-[image:--desktop-image] lg:bg-center',
    isDarkTheme ? 'theme-pmi-dark' : 'theme-pmi-light',
  );
  const bgCssVars = { ...desktopBgCssVariable(backgroundImage), ...mobileBgCssVariable(backgroundImageMobile) };

  return (
    <div ref={bgRef} className={`${bgStyles} w-full`} style={bgCssVars}>
      <PreloadBgImage
        bgImageDesktopSrc={fields?.HeroBackgroundImage?.value?.src}
        bgImageMobileSrc={fields?.HeroBackgroundImageMobile?.value?.src}
      />
      {fields?.HeroImage?.value?.src && <PreloadImage image={fields?.HeroImage?.value?.src} />}
      <div className="max-w-screen-2xl mx-auto">
        <div className="grid grid-cols-4 lg:grid-cols-12 gap-0 pt-[--scale-16] lg:pt-[--scale-24] pb-[--scale-44] lg:pb-[--scale-24] max-w-screen-xl mx-auto px-6 lg:px-4 grid-rows-[min-content_auto_max-content]">
          <div className="col-span-4 lg:col-span-12 h-min">
            <Placeholder name="spx-pdp-hero-header" rendering={rendering} />
          </div>
          <div className="h-full col-span-4 lg:col-span-12 grid grid-cols-4 lg:grid-cols-12 gap-0 lg:py-[--scale-24]">
            <div className="col-span-4 lg:col-span-5 h-[250px] lg:h-full flex">
              <div
                className={cn('lg:mt-[--scale-44] mt-[--scale-40] h-[--scale-80] w-fit lg:w-[--scale-320] lg:h-auto', {
                  'size-[200px] mt-[25px] lg:size-auto lg:mt-0 mx-auto self-center mb-[--scale-48]':
                    isMembership || isChapter,
                })}
              >
                {fields?.HeroImage?.value?.src && (
                  <ResponsiveImage image={fields?.HeroImage} preload={{ value: true }} />
                )}
              </div>
            </div>
            <div className="col-span-4 lg:!col-start-7 lg:col-span-6 flex flex-col items-start px-0">
              <Placeholder name="spx-pdp-hero-content" rendering={rendering} />
            </div>
          </div>
          <div className="col-span-4 md:col-span-12">
            <Placeholder name="spx-pdp-hero-footer" rendering={rendering} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default withEditorChromes(withProductFields(Component));
