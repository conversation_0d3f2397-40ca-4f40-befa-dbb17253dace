import { Helmet } from 'react-helmet';
import { Placeholder, useSitecoreContext } from '@sitecore-jss/sitecore-jss-react';
import { StaticlyBoundRendering, DynamicResourcePrefetch, Sticky } from '@pmi/www-shared/components';
import { AdobeAnalytics } from '@pmi/www-shared/analytics';
import { useLogRocketTracking, useIsMobileApp } from '@/hooks';
import { PageMetaData } from '@/models/Product';
import { themes } from '@pmi/www-shared/models';
import { parseHtmlTitle } from '@/utils';
import { DynamicScriptComponent, DynamicMetadataComponent } from './HelperComponents';
import { PageFields } from './models';

export const ClientLayout: React.FC = () => {
  const context = useSitecoreContext();
  const isMobileApp = useIsMobileApp();
  const routeData = context.sitecoreContext.route!;
  const htmlTitle = (routeData?.fields as PageMetaData)?.HtmlTitle?.value as string;
  const parsedTitle = parseHtmlTitle(htmlTitle);

  const theme = (routeData.fields as PageFields)?.Theme?.fields?.Value?.value;
  const selectedTheme = (theme ? themes[theme as keyof typeof themes] : themes.light) ?? themes.light;
  const bodyClassName = `${selectedTheme} overflow-x-hidden`;

  useLogRocketTracking();

  return (
    <>
      <Helmet>
        <title>{parsedTitle}</title>
        <body className={bodyClassName} />
      </Helmet>
      <DynamicScriptComponent />
      <DynamicMetadataComponent />
      <DynamicResourcePrefetch />
      {!isMobileApp && <StaticlyBoundRendering component={{ componentName: 'Adaptive Header' }} />}
      <Sticky />
      <div id="main-layout" className="overflow-x-hidden">
        <Placeholder rendering={routeData} name="app-main" />
      </div>
      <Sticky footer />
      {isMobileApp ? (
        <div className="my-[--scale-36]" />
      ) : (
        <StaticlyBoundRendering component={{ componentName: 'Adaptive Footer' }} />
      )}
      <AdobeAnalytics />
    </>
  );
};
