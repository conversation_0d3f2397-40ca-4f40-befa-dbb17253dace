import { ElearningItem, NameValuePair } from '@/models';
import * as Jss from '@/models/Jss';

export interface ProductOverviewLanguagesProps extends Jss.Rendering<ElearningItem> {}

export interface InnerItemViewProps {
  language: NameValuePair;
  selected: boolean;
}

export interface SelectedItemViewProps {
  language: NameValuePair;
}

export interface ItemViewProps {
  language: NameValuePair;
}
