import { type FC } from 'react';

export const withErrorHandling = <T,>(Component: FC<T>) => {
  return (props: T) => {
    try {
      return <Component {...props} />;
    } catch (err) {
      return (
        <>
          <div>Component {Component.name} did not render properly. Error details are below:</div>
          <div>${err.stack}</div>
          <div>${err.message}</div>
        </>
      );
    }
  };
};
