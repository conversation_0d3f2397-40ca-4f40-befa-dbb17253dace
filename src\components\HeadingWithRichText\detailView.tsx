import React from 'react';
import { withEditorChromes, Text, RichText } from '@sitecore-jss/sitecore-jss-react';
import { useIsExperienceEditor } from '@/hooks';
import { cn } from '@pmi/catalyst-utils';
import { HeadingWithRichTextProps } from './models';
import { MissingDataSource } from '../common';

const Component: React.FC<HeadingWithRichTextProps> = ({ fields }) => {
  const { Title: title, Content: content } = fields;

  const isExperienceEditor = useIsExperienceEditor();
  if (!title?.value) {
    return isExperienceEditor ? <MissingDataSource /> : null;
  }

  const headingStyle = fields?.renderingParameters?.HeadingStyle;
  const headingTag = headingStyle?.fields?.Tag?.value || 'h2';
  const headingStyles = cn(
    headingStyle?.fields?.Class?.value || 'font-medium text-header-md lg:text-header-lg',
    'col-span-4 lg:col-span-5',
  );

  return (
    <div className="grid grid-cols-4 lg:grid-cols-12 gap-[--scale-24] lg:gap-0">
      <Text field={title} tag={headingTag} className={headingStyles} />
      <RichText
        field={content}
        tag="div"
        className="rtb-context col-span-4 lg:!col-start-7 lg:col-span-6 font-normal text-body-sm lg:text-body-md"
      />
    </div>
  );
};

export default withEditorChromes(Component);
