import React from 'react';
import { ScriptComponentProps } from '../models';

export const ScriptComponent: React.FC<ScriptComponentProps> = (props) => {
  return (
    <>
      {props.cssFiles.map((_) => (
        <link key={_} href={_} rel="stylesheet" />
      ))}
      {props.jsFiles.map((_) => (
        <script key={_} defer src={_} />
      ))}
      {props.deferredJsFiles.map((_) => (
        <script key={_} defer src={_} />
      ))}
    </>
  );
};
