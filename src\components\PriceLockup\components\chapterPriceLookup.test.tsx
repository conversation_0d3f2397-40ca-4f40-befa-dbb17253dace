import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';

import * as utils from '@/utils';
import { ChapterPriceLockup } from './chapterPriceLockup';
import { usePriceLookupContext } from '../context';
import { usePriceLockupLogic } from '../hooks/usePriceLockupLogic';

// Mock dependencies
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock('../context');
jest.mock('../hooks/usePriceLockupLogic');
jest.mock('@/utils');
jest.mock('./alreadyInCart', () => ({
  AlreadyInCart: jest.fn(() => <div data-testid="already-in-cart" />),
}));
jest.mock('@sitecore-jss/sitecore-jss-react', () => ({
  withEditorChromes: (component: any) => component,
}));

describe('ChapterPriceLockup', () => {
  const mockPriceLookupContext = {
    isInCart: false,
    isMembershipInCart: false,
    isLoading: false,
    isAuthenticated: true,
  };

  const mockPriceLockupLogic = {
    productInfo: {
      currencyCode: 'USD',
      currencySymbol: '$',
      priceToShow: 99,
      productSku: 'TEST-SKU',
      isDeleted: false,
    },
    fields: {
      becomeMemberButtonText: { value: 'Become Member' },
      chapterDescriptionText: { value: 'Description' },
      learnMoreText: { value: 'learnMoreText' },
      loginLinkText: { value: 'loginLinkText' },
    },
    membershipInfo: {
      isMember: true,
      currentChapter: { endDate: '2024-12-31' },
      canRenew: false,
      isStudentMember: false,
    },
    isIndiaStore: false,
    settings: { MypmiUrl: 'https://test.com' },
    isProductSkuInActiveChapterMemberships: () => true,
    isLoading: false,
  };

  beforeEach(() => {
    (usePriceLookupContext as jest.Mock).mockReturnValue(mockPriceLookupContext);
    (usePriceLockupLogic as jest.Mock).mockReturnValue(mockPriceLockupLogic);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render loading state', () => {
    (usePriceLockupLogic as jest.Mock).mockReturnValue({
      ...mockPriceLockupLogic,
      isLoading: true,
    });

    const { container } = render(
      <ChapterPriceLockup studentPrice={49} isSticky={false} learnMoreAboutMembershipLink="test-link" />,
    );

    expect(container.firstChild).toBeNull();
  });

  it('should render prices correctly', () => {
    render(<ChapterPriceLockup studentPrice={49} isSticky={false} learnMoreAboutMembershipLink="test-link" />);

    expect(screen.getByText('Annual fee')).toBeInTheDocument();
    expect(screen.getByText('Student Price')).toBeInTheDocument();
  });

  it('should show correct button for non-member state', () => {
    (usePriceLockupLogic as jest.Mock).mockReturnValue({
      ...mockPriceLockupLogic,
      membershipInfo: { ...mockPriceLockupLogic.membershipInfo, isMember: false },
    });

    render(<ChapterPriceLockup studentPrice={49} isSticky={false} learnMoreAboutMembershipLink="test-link" />);

    const button = screen.getByText('Become Member');
    expect(button).toBeInTheDocument();

    fireEvent.click(button);
    expect(utils.navigateToCartPageWithMembership).toHaveBeenCalledWith('individual', expect.any(String));
  });

  it('should handle India store specific content', () => {
    (usePriceLockupLogic as jest.Mock).mockReturnValue({
      ...mockPriceLockupLogic,
      isIndiaStore: true,
    });

    render(<ChapterPriceLockup studentPrice={49} isSticky={false} learnMoreAboutMembershipLink="test-link" />);

    expect(screen.getByText('Available with a membership')).toBeInTheDocument();
  });

  it('should show learn more link in correct states', () => {
    (usePriceLookupContext as jest.Mock).mockReturnValue({
      ...mockPriceLookupContext,
      isAuthenticated: false,
    });

    render(<ChapterPriceLockup studentPrice={49} isSticky={false} learnMoreAboutMembershipLink="test-link" />);

    expect(screen.getByText('learnMoreText')).toBeInTheDocument();
    expect(screen.getByText('loginLinkText')).toBeInTheDocument();
  });

  it('should disable button when product is in cart and alreadyInCart visible', () => {
    (usePriceLookupContext as jest.Mock).mockReturnValue({
      ...mockPriceLookupContext,
      isInCart: true,
    });

    const { getByTestId } = render(
      <ChapterPriceLockup studentPrice={49} isSticky={false} learnMoreAboutMembershipLink="test-link" />,
    );

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(getByTestId('already-in-cart')).toBeInTheDocument();
  });

  it('should show correct expiration message for current member', () => {
    const endDate = new Date('2024-12-31').toLocaleDateString();
    render(<ChapterPriceLockup studentPrice={49} isSticky={false} learnMoreAboutMembershipLink="test-link" />);

    expect(screen.getByText((content) => content.includes(endDate))).toBeInTheDocument();
  });
});
