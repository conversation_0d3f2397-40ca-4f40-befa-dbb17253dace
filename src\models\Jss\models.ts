import { PlaceholdersData } from '@sitecore-jss/sitecore-jss/layout';

export interface BaseDataSourceItem {
  id: string;
}

export interface BaseRenderingParam {
  caching?: string;
}
export interface SitecoreRendering {
  componentName: string;
  uid: string;
  placeholders?: PlaceholdersData;
}

export interface SitecoreLocation {
  pathname: string;
}

export interface Item<TDataSourceItem> {
  fields: TDataSourceItem;
  url: string;
}

export interface SitecoreRenderingWithParams<TParameters> extends SitecoreRendering {
  params: TParameters;
}

export interface RenderingWithParams<TDataSourceItem extends BaseDataSourceItem, TParameters extends BaseRenderingParam>
  extends Rendering<TDataSourceItem> {
  rendering: SitecoreRenderingWithParams<TParameters>;
  params: TParameters;
}

export interface Rendering<TDataSourceItem extends BaseDataSourceItem = BaseDataSourceItem>
  extends Item<TDataSourceItem> {
  actionCallback: any;
  actions?: any;
  dictionary: Record<string, string>;
  language: string;
  rendering: SitecoreRendering;
  routeFields: any;
  sitecoreContext: any;
  fields: TDataSourceItem;
  location: SitecoreLocation;
}
