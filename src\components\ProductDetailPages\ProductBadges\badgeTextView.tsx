import React from 'react';
import { cn } from '@pmi/catalyst-utils';
import { BadgeTextViewProps } from './models';

const BadgeTextView: React.FC<BadgeTextViewProps> = ({ icon, text }) => {
  const textStyles = cn(
    icon === 'MembershipIcon' &&
      'text-transparent bg-clip-text bg-gradient-to-r from-[--colors-violet-700] via-[--colors-violet-500] to-[--colors-tangerine-600]',
  );

  return <div className={textStyles}>{text}</div>;
};

export default BadgeTextView;
