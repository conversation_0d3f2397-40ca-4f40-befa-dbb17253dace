import { DialogContent, DialogOverlay, DialogPortal, DialogRoot, DialogTitle } from '@pmi/catalyst-dialog';
import { cn } from '@pmi/catalyst-utils';
import { Spinner } from '@pmi/catalyst-spinner';

interface LoadingOverlayProps {
  /**
   * Modal dialog open indicator.
   */
  open?: boolean;
  /**
   * Modal dialog description.
   */
  description?: string;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ open, description }) => {
  return (
    <DialogRoot open={open}>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent
          onInteractOutside={(e) => {
            e.preventDefault();
          }}
          onEscapeKeyDown={(e) => {
            e.preventDefault();
          }}
          className={cn(
            'fixed top-[50%] left-[50%] h-[480px] w-[312px] lg:w-[932px]',
            'translate-x-[-50%] translate-y-[-50%]',
            'rounded-[--rounded-md] bg-[--surface-primary] px-[--scale-24] pb-[--scale-32] lg:px-[--scale-40] lg:pb-[--scale-40] pt-[--scale-80] focus:outline-none',
            'flex justify-start items-start gap-[--scale-12]',
          )}
        >
          <div className="flex flex-col w-[264px] lg:w-[500px] self-stretch justify-between">
            <div className="items-start">
              <DialogTitle
                className={cn(
                  'bg-gradient-to-b bg-clip-text',
                  'from-[#4F17A8] to-[#190B31]',
                  'font-medium text-left text-transparent text-header-lg lg:text-header-2xl pb-5',
                )}
              >
                {description}
              </DialogTitle>
            </div>
            <div className="flex justify-start self-stretch">
              <Spinner className="!w-[--scale-48] h-[--scale-48]" />
            </div>
          </div>
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  );
};
