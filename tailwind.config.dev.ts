import { default as currentConfig } from './tailwind.config';
import { readAllClientAssetsByClientCodePath } from '@pmi/shared-component-factory/serverRuntime';
import path from 'path';

const assets = readAllClientAssetsByClientCodePath(
  path.join(__dirname, process.env.OUTPUT_PATH as string, process.env.ROOT_FOLDER as string),
);
const otherComponentFactories = assets.allAppAssetsByApp.map((_) =>
  path.join(__dirname, process.env.OUTPUT_PATH as string, _.jsAssets.componentFactoryFile),
);
const config: typeof currentConfig = {
  ...currentConfig,
  content: [...currentConfig.content, ...otherComponentFactories],
};

export default config;
