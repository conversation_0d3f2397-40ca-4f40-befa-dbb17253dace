import { PRODUCTDETAILPAGES_FIELDLABELS } from '@/constants';
import { withProductFields } from '@/hocs';
import { useRouteFields } from '@/hooks';
import { DigitalProductBase } from '@/models';
import { toFixedIfNecessary } from '@/utils';
import React from 'react';
import { withEditorChromes, Image } from '@sitecore-jss/sitecore-jss-react';
import { useTranslation } from 'react-i18next';
import { Separator } from '@pmi/catalyst-separator';
import { OnlineCourseBreakdownContentProps } from './models';

const Component: React.FC<OnlineCourseBreakdownContentProps> = ({ fields }) => {
  const product = useRouteFields<DigitalProductBase>();
  const { TotalPDUsImage, BusinessAcumenImage, WaysOfWorkingImage, PowerSkillsImage } = fields;
  const { t: dictionaryTranslation } = useTranslation();

  const totalPDUsLabel = dictionaryTranslation(PRODUCTDETAILPAGES_FIELDLABELS.ELEARNING.TOTALPDUS, 'Total PDUs');
  const businessAcumenLabel = dictionaryTranslation(
    PRODUCTDETAILPAGES_FIELDLABELS.ELEARNING.BUSINESSACUMEN,
    'Business acumen',
  );
  const waysOfWorkingLabel = dictionaryTranslation(
    PRODUCTDETAILPAGES_FIELDLABELS.ELEARNING.WAYSOFWORKING,
    'Ways of working',
  );
  const powerSkillsLabel = dictionaryTranslation(PRODUCTDETAILPAGES_FIELDLABELS.ELEARNING.POWERSKILLS, 'Power skills');

  return (
    <div>
      <Separator className="w-full" />
      <div className="max-w-screen-2xl mx-auto">
        <div className="max-w-screen-xl mx-auto px-0 lg:px-4 flex flex-col lg:flex-row lg:h-[400px]">
          <Separator className="hidden lg:block" orientation="vertical" />
          <div className="w-full lg:max-w-[400px] p-[--scale-24] flex flex-col bg-gradient-to-b from-[#170A2A] to-[#2B1B45] min-h-[291px] lg:h-full">
            <h2 className="lg:h-[56px] flex-none text-[--text-white] text-header-sm font-medium">{totalPDUsLabel}</h2>
            <p className="flex-none text-[--text-white] text-header-md font-medium pt-[--scale-12]">
              {toFixedIfNecessary(+product?.pdus?.value, 2)}
            </p>
            <div className="flex flex-col justify-end shrink self-end w-[200px] lg:w-[282px] h-full">
              <Image field={TotalPDUsImage} />
            </div>
          </div>
          <Separator className="lg:hidden" />
          <Separator className="hidden lg:block" orientation="vertical" />
          <div className="grid grid-col-2 lg:flex lg:flex-col p-[--scale-24] flex-1 min-h-[142px] lg:h-full">
            <h2 className="lg:max-w-[120px] col-span-2 text-[--text-neutral-soft] text-header-sm font-medium">
              {businessAcumenLabel}
            </h2>
            <p className="pt-[--scale-12] text-primary text-header-md font-medium">
              {toFixedIfNecessary(+product?.strategic_business_pdus?.value, 2)}
            </p>
            <div className="flex-1 flex flex-col justify-end justify-self-end self-end w-[69px]">
              <Image field={BusinessAcumenImage} />
            </div>
          </div>
          <Separator className="lg:hidden" />
          <Separator className="" orientation="vertical" />
          <div className="grid grid-col-2 lg:flex lg:flex-col p-[--scale-24] flex-1 min-h-[142px] lg:h-full">
            <h2 className="lg:max-w-[120px] col-span-2 text-[--text-neutral-soft] text-header-sm font-medium">
              {waysOfWorkingLabel}
            </h2>
            <p className="pt-[--scale-12] text-primary text-header-md font-medium">
              {toFixedIfNecessary(+product?.technical_pdus?.value, 2)}
            </p>
            <div className="flex-1 flex flex-col justify-end justify-self-end self-end w-[69px]">
              <Image field={WaysOfWorkingImage} />
            </div>
          </div>
          <Separator className="lg:hidden" />
          <Separator className="hidden lg:block" orientation="vertical" />
          <div className="grid grid-col-2 lg:flex lg:flex-col p-[--scale-24] flex-1 min-h-[142px] lg:h-full">
            <h2 className="lg:max-w-[120px] col-span-2 text-[--text-neutral-soft] text-header-sm font-medium">
              {powerSkillsLabel}
            </h2>
            <p className="pt-[--scale-12] text-primary text-header-md font-medium">
              {toFixedIfNecessary(+product?.leadership_pdus?.value, 2)}
            </p>
            <div className="flex-1 flex flex-col justify-end justify-self-end self-end w-[69px]">
              <Image field={PowerSkillsImage} />
            </div>
          </div>
          <Separator className="" orientation="vertical" />
        </div>
      </div>
      <Separator className="w-full" />
    </div>
  );
};

export default withEditorChromes(withProductFields(Component));
