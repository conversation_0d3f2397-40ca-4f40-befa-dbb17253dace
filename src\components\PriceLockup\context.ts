import { createContext, useContext } from 'react';
import { CommerceProductBase } from '@/models';
import { PriceLockupProps } from './models';

export interface PriceLookupValue extends Pick<PriceLockupProps, 'rendering' | 'fields'> {
  product: CommerceProductBase | null;
  isAuthenticated: boolean;
  isInCart: boolean;
  isMembershipInCart: boolean;
  isLoading: boolean;
}

export const PriceLookupContext = createContext<PriceLookupValue>({
  rendering: null,
  fields: null,
  product: null,
  isAuthenticated: false,
  isInCart: false,
  isMembershipInCart: false,
  isLoading: false,
});
export const usePriceLookupContext = () => useContext(PriceLookupContext);
