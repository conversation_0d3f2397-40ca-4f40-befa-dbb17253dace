import React from 'react';
import { useScreenSize } from '@pmi/www-shared/hooks';
import { MobileStickyFooterProps } from './models';

const Component: React.FC<MobileStickyFooterProps> = ({ children, isSticky = false }) => {
  const isMobile = useScreenSize();
  return isSticky && isMobile ? (
    <div className="fixed bottom-0 left-0 z-[1000] w-full px-6 py-4 backdrop-blur-[50px]">{children}</div>
  ) : (
    children
  );
};

export default Component;
