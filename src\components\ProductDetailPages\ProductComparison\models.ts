import * as Jss from '@/models/Jss';
import { RichTextField, TextField, Field } from '@sitecore-jss/sitecore-jss-react';

export interface ProductComparisonDataSource extends Jss.BaseDataSourceItem {
  Title: TextField;
  ProductLinkText: Field<string>;
  SubText: RichTextField;
  productsWithBenefits: ProductWithBenefits[];
}

export interface ProductComparisonRenderingParams extends Jss.BaseRenderingParam {
  highlightedProductSku: string;
  highlightedColumn: number;
}

export interface ProductComparisonProps
  extends Jss.RenderingWithParams<ProductComparisonDataSource, ProductComparisonRenderingParams> {}

export interface ProductWithBenefits {
  id: string;
  name: string;
  productSku: string;
  description: string;
  benefits: ProductBenefit[];
  highlightCurrentItem: boolean;
  customTitle: string;
  customPrice: string;
  hidePrice: boolean;
  actionLink: BenefitActionLink;
}

export interface ProductBenefit {
  id: string;
  benefitTypeId: string;
  benefitTypeTitle: string;
  benefitTypeDescription: string;
  benefitTextValue: string;
  benefitCheckmarkValue: boolean;
  benefitDataTypeId: string;
  benefitDataTypeName: string;
}

export interface BenefitActionLink {
  url: string;
  text: string;
}

export interface Header {
  key: string;
  actionLink?: {
    text: string;
    url: string;
  };
  description: string;
  price: string;
  hidePrice: boolean;
  sku: string;
  title: string;
}

export interface HeaderViewProps {
  highlightedColumn: number;
  headers: Header[];
}

export interface BodyRow {
  key: string;
  columnValues: BodyColumnValue[];
  description: string;
  title: string;
}

export interface BodyColumnValue {
  key: string;
  value: string | boolean;
}

export interface BodyRowViewProps {
  highlightedColumn: number;
  rows: BodyRow[];
}
