import { ProductApiModel } from '@/store';

export interface ProductCardProps {
  product: ProductApiModel;
  shortDescription: string;
  buttonText: string;
  showPrices: boolean;
  showCTA: boolean;
  membershipTextHighlightColor: string;
}

export enum HighlightTextColorMap {
  Black = 'bg-[--fill-off-black]',
  Cyan = 'bg-[--fill-info-soft]',
  Grey = 'bg-[--fill-off-black-softer]',
  Orange = 'bg-[--fill-warning-soft]',
  Purple = 'bg-[--fill-accent-softer]',
}
