import { useScreenSize } from '@pmi/www-shared/hooks';
import { MissingDataSource } from '@/components/common';
import { useIsExperienceEditor } from '@/hooks/Sitecore';
import { PdpCardsHolderDesktopView } from './desktopView';
import { PdpCardsHolderMobileView } from './mobileView';
import { CardsHolderProps } from './models';

export const PdpCardsHolder: React.FC<CardsHolderProps> = (props) => {
  const isMobile = useScreenSize();
  const isExperienceEditor = useIsExperienceEditor();

  if (!props?.fields?.Title) {
    return isExperienceEditor ? <MissingDataSource /> : null;
  }

  return isMobile ? <PdpCardsHolderMobileView {...props} /> : <PdpCardsHolderDesktopView {...props} />;
};
