import { buildInteractiveResult, buildResultList, buildSearchEngine } from '@coveo/headless';
import { render } from '@testing-library/react';
import { MembershipsExtended } from '@pmi/www-shared/store';
import ProductResultList from './ProductResultList';

jest.mock('@coveo/headless', () => ({
  ...jest.requireActual('@coveo/headless'),
  buildResultList: jest.fn(),
  buildSearchEngine: jest.fn(),
  buildInteractiveResult: jest.fn(),
}));

describe('ProductResultList', () => {
  it('should render result list', () => {
    const headlessEngine = buildSearchEngine({
      configuration: {
        organizationId: 'orgId',
        accessToken: '12345',
      },
    });
    const fieldsToInclude = ['producttitle'];
    const membership: MembershipsExtended = {
      chapterMemberships: [],
      subscriptions: [],
    };
    const results = [
      {
        title: 'PMI Educational Foundation (PMIEF)',
        isTopResult: false,
        raw: {
          producttitle: 'PMI Educational Foundation (PMIEF)',
          permanentid: '123456',
        },
      },
      {
        title: 'Membership SKU',
        isTopResult: false,
        raw: {
          producttitle: 'Membership SKU',
          permanentid: '123456',
        },
      },
    ];
    (buildResultList as jest.Mock).mockReturnValue({
      state: {
        hasError: false,
        isLoading: false,
        hasResults: true,
        firstSearchExecuted: true,
        results,
      },
      subscribe: (callback: () => void) => callback(),
    });
    (buildInteractiveResult as jest.Mock).mockReturnValue({ select: () => false });

    const resultList = buildResultList(headlessEngine, {
      options: {
        fieldsToInclude,
      },
    });

    const { container } = render(
      <ProductResultList controller={resultList} storeResolvingCountry="en" membership={membership} />,
    );
    expect(container.firstChild).toMatchSnapshot();
  });
});
