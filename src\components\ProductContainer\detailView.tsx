import { ProductContext } from '@/containers';
import { useRouteFields } from '@/hooks';
import { CommerceProductBase } from '@/models';
import { Placeholder } from '@sitecore-jss/sitecore-jss-react';
import { FC, useMemo } from 'react';
import { ProductContainerProps } from './models';

const ProductContainer: FC<ProductContainerProps> = (props) => {
  let product = useRouteFields<CommerceProductBase>();
  if (props.fields) {
    product = props.fields;
  }

  const contextValue = useMemo(() => {
    return {
      product,
    };
  }, [product]);

  return (
    <ProductContext.Provider value={contextValue}>
      <Placeholder name="spx-product-container" rendering={props.rendering} />
    </ProductContext.Provider>
  );
};

export default ProductContainer;
