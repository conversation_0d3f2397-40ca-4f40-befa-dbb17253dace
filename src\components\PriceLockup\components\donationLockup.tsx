import React, { useMemo, useState } from 'react';
import NumberFormat from 'react-number-format';
import { cn } from '@pmi/catalyst-utils';
import { Card } from '@pmi/catalyst-card';
import { ButtonComponent } from '@/components/ui';
import { useRouteFields } from '@/hooks';
import { DonationProductItem } from '@/models';
import { navigateToCartPageWithDonation } from '@/utils';
import { usePriceLockupTexts } from '../usePriceLockupTexts';
import { DonationLockupProps } from '../models';
import { usePriceLookupContext } from '../context';
import { AlreadyInCart } from './alreadyInCart';

const donationTextInputLabel = 'donationTextInput';

const DonationLockup: React.FC<DonationLockupProps> = ({ currencySymbol }) => {
  const { isInCart, isLoading, product } = usePriceLookupContext();
  const { addToCartButtonText, donationPlaceholderText, donationTitleField } = usePriceLockupTexts();
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null);
  const [customAmount, setCustomAmount] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  const {
    donation_fixed_amount: { value: donationFixedAmount },
    donation_max_amount: { value: donationMaxAmount },
    experius_donation_min_amount: { value: donationMinAmount },
  } = useRouteFields<DonationProductItem>();

  const donationAmountOptions = (donationFixedAmount as string).split(',').map(Number);
  const disabledCTA = useMemo(
    () =>
      product?.IsDeleted?.value === true ||
      isLoading ||
      isInCart ||
      (customAmount && (Number(customAmount) < donationMinAmount || Number(customAmount) > donationMaxAmount)) ||
      (!customAmount && !selectedAmount),
    [
      product?.IsDeleted?.value,
      isLoading,
      isInCart,
      customAmount,
      donationMinAmount,
      donationMaxAmount,
      selectedAmount,
    ],
  );
  const handleAmountClick = (amount: number) => {
    setSelectedAmount(amount);
    setCustomAmount('');
    setError(null);
  };

  const handleCustomAmountChange = (values: any) => {
    const { value } = values;
    const intValue = value.replace(/\D/g, '');
    if (intValue !== customAmount) {
      setCustomAmount(intValue);
      setSelectedAmount(null);

      const floatValue = parseFloat(intValue);
      if (floatValue < donationMinAmount) {
        setError(`Minimum donation amount is ${donationMinAmount}`);
      } else if (floatValue > donationMaxAmount) {
        setError(`Maximum donation amount is ${donationMaxAmount}`);
      } else {
        setError(null);
      }
    }
  };

  const handleAddToCart = () => {
    const amount = selectedAmount || customAmount;
    navigateToCartPageWithDonation(amount);
  };

  return (
    <div className="col-span-6 grid grid-cols-6">
      <h2 className="col-span-6 body-text-sm font-normal">{donationTitleField}</h2>
      <div className="col-span-6 grid grid-cols-6 pt-[--scale-16] gap-[--scale-8]">
        {donationAmountOptions.map((amount) => (
          <Card
            key={amount}
            className={cn(
              'col-span-3 lg:col-span-2 py-[--scale-12] px-[--scale-16] cursor-pointer rounded-[--rounded-xs] border border-[--border-neutral]',
              { 'outline outline-4': selectedAmount === amount },
            )}
            onClick={() => handleAmountClick(amount)}
          >
            <span className="text-header-xs font-medium">
              {currencySymbol}
              {amount}
            </span>
          </Card>
        ))}
      </div>
      <div className="col-span-6 grid grid-cols-6 gap-[--scale-24] pt-[--scale-16] lg:pt-[--scale-12]">
        <div className="col-span-6 lg:col-span-4">
          <NumberFormat
            className={cn('w-full py-2 px-3 border rounded', { 'border-red-500': error })}
            name={donationTextInputLabel}
            decimalScale={0}
            isNumericString
            thousandSeparator
            allowNegative={false}
            allowLeadingZeros={false}
            prefix={currencySymbol}
            maxLength={12}
            onValueChange={handleCustomAmountChange}
            value={customAmount}
            placeholder={donationPlaceholderText}
          />
          {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
          <AlreadyInCart display={isInCart} />
        </div>
        <ButtonComponent
          classNames="col-span-6 lg:col-span-2 h-[--scale-48]"
          variant="solid"
          buttonText={addToCartButtonText}
          onClick={handleAddToCart}
          size="sm"
          disabled={disabledCTA}
        />
      </div>
    </div>
  );
};

export default DonationLockup;
