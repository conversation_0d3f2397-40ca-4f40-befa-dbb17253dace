import { render } from '@testing-library/react';
import { useIsExperienceEditor, useRouteFields } from '@/hooks';
import ProductShortDescription from './detailView';

jest.mock('@/hooks', () => ({
  useIsExperienceEditor: jest.fn(),
  useRouteFields: jest.fn(),
}));

jest.mock('@sitecore-jss/sitecore-jss-react', () => ({
  useSitecoreContext: jest.fn(),
  withEditorChromes: (component: any) => component,
  Text: () => {
    return <div>shortDescription</div>;
  },
}));

jest.mock('@/hocs', () => ({
  withProductFields: (component: any) => component,
}));

describe('ProductShortDescription detailView tests', () => {
  it('should be rendered empty', () => {
    (useIsExperienceEditor as jest.Mock).mockReturnValue(false);
    (useRouteFields as jest.Mock).mockReturnValue({});
    const { container } = render(<ProductShortDescription />);

    expect(container.firstChild).toBeNull();
  });

  it('should MissingDataSource be rendered ', () => {
    (useIsExperienceEditor as jest.Mock).mockReturnValue(true);
    (useRouteFields as jest.Mock).mockReturnValue({});
    const expectedValue = 'This component is missing a data source';

    const { container } = render(<ProductShortDescription />);

    expect(container.firstChild).toContainHTML(expectedValue);
  });

  it('should be rendered', () => {
    const shortDescriptionValue = 'shortDescription';
    (useIsExperienceEditor as jest.Mock).mockReturnValue(true);
    (useRouteFields as jest.Mock).mockReturnValue({ Abstract: { value: 'shortDescription' } });

    const { getByText } = render(<ProductShortDescription />);
    const component = getByText(shortDescriptionValue);

    expect(component).toBeVisible();
  });
});
