import React from 'react';
import { withEditor<PERSON>hromes, RichText } from '@sitecore-jss/sitecore-jss-react';
import { withProductFields } from '@/hocs';
import { useScreenSize } from '@pmi/www-shared/hooks';
import { SubscriptionChartProps } from './models';
import * as Desktop from './components/desktop';
import * as Mobile from './components/mobile';
import { HeaderRow } from './components/models';

const Component: React.FC<SubscriptionChartProps> = ({ fields }) => {
  const { Title, SubscriptionChartHeader, SubscriptionChartBody, SubText } = fields;
  const headerData = SubscriptionChartHeader?.value as string;
  const bodyData = SubscriptionChartBody?.value as string;
  const isMobile = useScreenSize();

  const headerRows = JSON.parse(headerData as string);
  const selectedRowIndex = headerRows.findIndex((headRow: HeaderRow) => headRow.isSelected === true);
  const leftColumnCondition = selectedRowIndex === 0;
  const rightColumnCondition = selectedRowIndex === 1;

  return (
    <>
      <h1 className="text-header-lg pb-20">{Title?.value}</h1>
      <div className="mobile:overflow-x-auto mobile:mobile-scrollbar mobile:min-w-60 mobile:pb-4">
        <table className="w-full table-auto border-separate border-spacing-0 [&_td]:mobile:w-1/2 [&_td]:mobile:min-w-60">
          <thead>
            <tr>
              {!isMobile && <th>&nbsp;</th>}
              {isMobile ? (
                <Mobile.HeaderRowView
                  data={headerData}
                  leftColumnCondition={leftColumnCondition}
                  rightColumnCondition={rightColumnCondition}
                />
              ) : (
                <Desktop.HeaderRowView
                  data={headerData}
                  leftColumnCondition={leftColumnCondition}
                  rightColumnCondition={rightColumnCondition}
                />
              )}
            </tr>
          </thead>
          <tbody>
            {isMobile ? (
              <Mobile.BodyRowView
                data={bodyData}
                leftColumnCondition={leftColumnCondition}
                rightColumnCondition={rightColumnCondition}
              />
            ) : (
              <Desktop.BodyRowView
                data={bodyData}
                leftColumnCondition={leftColumnCondition}
                rightColumnCondition={rightColumnCondition}
              />
            )}
          </tbody>
          {!isMobile && (
            <tfoot>
              <tr>
                <td>&nbsp;</td>
                <td className="pt-6 text-body-sm rtb-context" colSpan={2}>
                  <RichText field={SubText} />
                </td>
              </tr>
            </tfoot>
          )}
        </table>
      </div>
      {isMobile && <RichText field={SubText} className="mt-6 text-body-sm rtb-context" />}
    </>
  );
};

export default withEditorChromes(withProductFields(Component));
