type RequestMiddleware = (url: string, options: RequestOptions) => Promise<{ url: string; options: RequestOptions }>;
type ResponseMiddleware = (response: Response) => Promise<Response>;

type RequestOptions = {
  method?: string;
  headers?: { [key: string]: string };
  body?: any;
  signal?: AbortSignal;
};

const fetchWrapper = async <TResult>(
  url: string,
  options: RequestOptions = {},
  requestMiddleware: RequestMiddleware[] = [],
  responseMiddleware: ResponseMiddleware[] = [],
): Promise<TResult> => {
  for (const middleware of requestMiddleware) {
    const result = await middleware(url, options);
    url = result.url;
    options = result.options;
  }

  const { method = 'GET', headers = {}, body, signal } = options;
  let response = await fetch(url, {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
    body: body ? JSON.stringify(body) : undefined,
    signal,
  });

  for (const middleware of responseMiddleware) {
    response = await middleware(response);
  }

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

export default fetchWrapper;
