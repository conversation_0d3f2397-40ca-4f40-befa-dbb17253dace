import { buildInteractiveResult, buildSearchEngine } from '@coveo/headless';
import { render } from '@testing-library/react';
import { useContext } from 'react';
import { SearchResult } from '.';
import { ProductResult, SearchResultProps } from '../../models';

jest.mock('@coveo/headless', () => ({
  ...jest.requireActual('@coveo/headless'),
  buildInteractiveResult: jest.fn(),
  baseFacetResponseSelector: jest.fn(),
}));
jest.mock('@pmi/www-shared/components', () => ({
  Facet: ({ title }: any) => <div>{title}</div>,
}));
jest.mock('@pmi/catalyst-badge', () => ({
  Badge: () => <div data-testId="catalyst-badge" />,
}));
jest.mock('@pmi/catalyst-link', () => ({
  Link: () => <div data-testId="catalyst-link" />,
}));

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useContext: jest.fn(),
}));

describe('result SearchResult', () => {
  beforeEach(() => {
    const searchEngine = buildSearchEngine({
      configuration: {
        organizationId: 'orgId',
        accessToken: '12345',
      },
    });
    (useContext as jest.Mock).mockReturnValue({
      searchEngine,
    });
    (buildInteractiveResult as jest.Mock).mockReturnValue({ select: () => false });
  });
  const defaultResult: ProductResult = {
    title: '',
    isTopResult: true,
    raw: {
      producttitle: '',
      permanentid: '',
      haslayout: '',
      abstract: '',
      productcountrypricing: '',
      productpriceunittype: '',
      globalz32xprice: 0,
      productmemberprice: 0,
      producttypetitle: '',
      urihash: '',
    },
    uri: '',
    printableUri: '',
    clickUri: '',
    uniqueId: '',
    excerpt: '',
    firstSentences: '',
    summary: null,
    flags: '',
    hasHtmlVersion: false,
    score: 0,
    percentScore: 0,
    rankingInfo: '',
    isRecommendation: false,
    titleHighlights: [],
    firstSentencesHighlights: [],
    excerptHighlights: [],
    printableUriHighlights: [],
    summaryHighlights: [],
    absentTerms: [],
    isUserActionView: false,
    searchUid: '',
  };
  const results: ProductResult[] = [
    {
      ...defaultResult,
      title: 'PMI Educational Foundation (PMIEF)',
      isTopResult: true,
      raw: {
        ...defaultResult.raw,
        producttitle: 'PMI_ProductTitle',
        permanentid: '1',
        haslayout: '1',
        productcountrypricing: '[]',
        producttypetitle: 'Membership',
        globalz32xprice: 100,
        productmemberprice: 200,
        productmemberorglobalprice: 200,
        productpriceunittype: '$',
      },
    },
    {
      ...defaultResult,
      title: 'Donation',
      isTopResult: false,
      raw: {
        ...defaultResult.raw,
        producttitle: 'Donation',
        permanentid: '2',
        producttypetitle: 'Donation',
      },
    },
  ];

  it('should return list results for Membership', () => {
    const props: SearchResultProps = {
      result: results[0],
      storeResolvingCountry: '',
      membership: {
        hasActiveMembership: true,
        chapterMemberships: [],
        subscriptions: [],
      },
    };

    const { getByTestId, getByText } = render(<SearchResult {...props} />);
    const badgeElement = getByTestId('catalyst-badge');
    expect(badgeElement).toBeTruthy();
    const linkElement = getByTestId('catalyst-link');
    expect(linkElement).toBeTruthy();
    expect(getByText('Member Price')).toBeTruthy();
    expect(getByText('USD $200')).toBeTruthy();
  });
  it('should return list results for Donation', () => {
    const props: SearchResultProps = {
      result: results[1],
      storeResolvingCountry: '',
      membership: undefined,
    };

    const { container } = render(<SearchResult {...props} />);
    expect(container.firstChild).toMatchSnapshot();
  });
});
