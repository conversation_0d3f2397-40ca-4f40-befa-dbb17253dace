import React from 'react';
import { ButtonComponent, ButtonComponentProps } from '@/components/ui';
import { useIsFreeProduct } from '@/hooks/Products';
import { navigateToCartPageWithSku, navigateToLoginWithRedirectToCartPageWithSku } from '@/utils';
import { SinglePriceLockupProps } from '../models';
import { usePriceLockupTexts } from '../usePriceLockupTexts';
import PriceDisplay from './priceDisplay';
import { ActivateFreeProductButton } from './activateFreeProductButton';
import { AlreadyInCart } from './alreadyInCart';
import { usePriceLookupContext } from '../context';

const SinglePriceLockup: React.FC<SinglePriceLockupProps> = ({
  regularPrice,
  currencyCode,
  currencySymbol,
  sku,
  fields,
}) => {
  const { regularPriceText, addToCartButtonText } = usePriceLockupTexts();
  const { isAuthenticated, isInCart, isLoading } = usePriceLookupContext();
  const { isFreeProductActivationEligible } = useIsFreeProduct(fields);

  const handleClick = () => {
    if (!isAuthenticated) {
      navigateToLoginWithRedirectToCartPageWithSku(sku, window?.location?.pathname);
    }
    navigateToCartPageWithSku(sku, window?.location?.pathname);
  };

  const disabledCTA = fields?.IsDeleted?.value === true || isLoading || isInCart;

  const buttonRenderingProps: ButtonComponentProps = {
    variant: 'solid',
    buttonText: '',
    onClick: () => {},
    size: 'sm',
    classNames: 'w-full lg:w-max',
    danger: false,
  };

  return (
    <>
      <div className="col-span-4">
        <PriceDisplay
          title={regularPriceText}
          price={regularPrice}
          currencyCode={currencyCode}
          currencySymbol={currencySymbol}
          isBold
        />
        <AlreadyInCart display={isInCart} />
      </div>
      <div className="col-span-4 lg:col-span-2 flex justify-end pt-[--scale-24] lg:pt-0">
        {isFreeProductActivationEligible ? (
          <ActivateFreeProductButton sku={sku} {...buttonRenderingProps} />
        ) : (
          <ButtonComponent
            classNames={buttonRenderingProps.classNames}
            disabled={disabledCTA}
            variant={buttonRenderingProps.variant}
            buttonText={addToCartButtonText}
            onClick={handleClick}
            danger={buttonRenderingProps.danger}
            size={buttonRenderingProps.size}
            mobileStickyFooter
          />
        )}
      </div>
    </>
  );
};

export default SinglePriceLockup;
