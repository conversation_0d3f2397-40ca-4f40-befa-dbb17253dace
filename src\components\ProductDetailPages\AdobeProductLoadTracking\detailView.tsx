import { useProductTracking } from '@pmi/www-shared/analytics';
import { useEffect } from 'react';
import { useIsFreeProduct, useRouteFields } from '@/hooks';
import { CommerceProductBase } from '@/models';
import { getProductType } from '@/utils';
import { useGetActiveMembershipsQuery } from '@pmi/www-shared/store';
import { UserStatuses, useUserStatus } from '@pmi/www-shared/hooks';

const Component: React.FC = () => {
  const product = useRouteFields<CommerceProductBase>();
  const { isFreeProductActivationEligible } = useIsFreeProduct(product);
  const userStatus = useUserStatus();
  const isAuthenticated = userStatus === UserStatuses.LoggedIn;
  const { isLoading: activeMembershipLoading } = useGetActiveMembershipsQuery(null, { skip: !isAuthenticated });
  const productTracking = useProductTracking();

  const sku = product?.ExternalID?.value as string;
  const productType = getProductType(product);

  useEffect(() => {
    productTracking();
  }, []);

  useEffect(() => {
    if (!activeMembershipLoading) {
      productTracking(productType, sku, isFreeProductActivationEligible);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeMembershipLoading]);

  return null;
};

export default Component;
