import { <PERSON>, LinkField, RichTextField, TextField } from '@sitecore-jss/sitecore-jss-react';
import { CoveoEngineProps } from '@pmi/www-shared/components';
import { BaseDataSourceItem } from '../Jss';
import { SitecoreTemplate } from '../Sitecore';

export interface PdpActionLinkDataDataSource extends BaseDataSourceItem {
  Link: LinkField;
}

export interface PdpCardsHolderDataDataSource extends BaseDataSourceItem {
  Title: TextField;
  SubCopy: TextField;
  Cards: any;
}

export interface PdpCardDataDataSource extends BaseDataSourceItem {
  Title: TextField;
  ModuleLabel?: TextField;
  ModuleNumber?: TextField;
  Description: TextField;
}

export interface ConfirmationDialogFields {
  confirmSuccessText?: Field<string>;
  confirmText?: Field<string>;
  confirmFooterText?: RichTextField;
  confirmButtonText?: Field<string>;
}

export interface ErrorModalWindowDataDataSource extends BaseDataSourceItem {
  Title: TextField;
  Body: RichTextField;
  ButtonText: TextField;
}

export interface PdpChapterActionLinkDataDataSource extends BaseDataSourceItem {
  LinkText: TextField;
}

export interface ProductSearchDataDataSource extends BaseDataSourceItem {
  SearchHub?: Field<string>;
  Templates?: SitecoreTemplate[];
  coveo: CoveoEngineProps;
}
