import React from 'react';
import { withEditorChromes, Text } from '@sitecore-jss/sitecore-jss-react';
import { useIsExperienceEditor, useRouteFields } from '@/hooks';
import { withProductFields } from '@/hocs';
import { CommerceProductBase } from '@/models';
import { MissingDataSource } from '@/components/common';
import { ProductShortDescriptionProps } from './models';

const Component: React.FC<ProductShortDescriptionProps> = () => {
  const { Abstract: shortDescription } = useRouteFields<CommerceProductBase>();

  const isExperienceEditor = useIsExperienceEditor();
  if (!shortDescription?.value) {
    return isExperienceEditor ? <MissingDataSource /> : null;
  }

  return (
    <p className="font-normal text-body-sm lg:text-body-md pt-[--scale-12]">
      <Text field={shortDescription} />
    </p>
  );
};

export default withEditorChromes(withProductFields(Component));
