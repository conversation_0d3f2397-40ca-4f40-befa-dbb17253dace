import React, { useEffect, useMemo, useRef } from 'react';
import { useInViewport } from 'ahooks';
import { withEditorChromes } from '@sitecore-jss/sitecore-jss-react';
import { withProductFields, withErrorHandling } from '@/hocs';
import { addStickyHeaderComponent, removeStickyHeaderComponent } from '@pmi/www-shared/store';
import { useIsExperienceEditor, useRouteFields } from '@/hooks';
import { useAppDispatch, UserStatuses, useScreenSize, useUserStatus } from '@pmi/www-shared/hooks';
import { StickyComponents } from '@/layouts/constants';
import { getProductType } from '@/utils';
import { PRODUCTTYPES } from '@/constants';
import { CommerceProductBase } from '@/models';
import { PriceLockupProps } from './models';

import { DesktopView } from './views';
import { PriceLookupContext, PriceLookupValue } from './context';
import { useInCart } from './hooks/useInCart';

const PriceLockup: React.FC<PriceLockupProps> = (props) => {
  const containerTop = useRef<number>();
  const isExperienceEditor = useIsExperienceEditor();
  const isMobile = useScreenSize();
  const container = useRef<HTMLDivElement>(null);
  const dispatch = useAppDispatch();
  const [inViewport] = useInViewport(container);
  const product = useRouteFields<CommerceProductBase>();
  const isDonationProduct = getProductType(product) === PRODUCTTYPES.Donation;
  const userStatus = useUserStatus();
  const isAuthenticated = userStatus === UserStatuses.LoggedIn;

  const { isInCart, isMembershipInCart, isLoading } = useInCart(
    product?.ExternalID?.value.toString().toLowerCase(),
    isAuthenticated,
    product?.is_membership.value,
  );

  const adobeRegion = {
    adoberegion: 'pdp-hero',
  };

  useEffect(() => {
    containerTop.current = container.current?.getBoundingClientRect()?.top;
  }, []);

  const contextValue = useMemo<PriceLookupValue>(() => {
    const { rendering, fields } = props;
    return {
      fields,
      rendering,
      product,
      isAuthenticated,
      isInCart,
      isMembershipInCart,
      isLoading,
    };
  }, [isAuthenticated, isInCart, isMembershipInCart, isLoading, product, props]);

  useEffect(() => {
    if (!isExperienceEditor && container.current && !isDonationProduct) {
      const sticky = !inViewport && !!container.current && window.scrollY > container.current.offsetTop;
      const { uid: id } = props.rendering ?? {};

      if (sticky) {
        if (!isMobile) {
          dispatch(
            addStickyHeaderComponent({
              id,
              position: containerTop.current,
              props: { ...props.fields, ...props.rendering, isSticky: true, contextValue },
              type: StickyComponents.PriceLockup,
            }),
          );
        }
      } else {
        dispatch(removeStickyHeaderComponent(id));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [inViewport, isDonationProduct]);

  return (
    <div ref={container} className="w-full" {...adobeRegion}>
      <PriceLookupContext.Provider value={contextValue}>
        <DesktopView {...props} containerRef={container} />
      </PriceLookupContext.Provider>
    </div>
  );
};

export default withEditorChromes(withProductFields(withErrorHandling(PriceLockup)));
