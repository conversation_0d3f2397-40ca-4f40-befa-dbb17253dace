import { withEditorChromes } from '@sitecore-jss/sitecore-jss-react';
import type { CTAButtonProps } from './models';
import { ButtonComponent, ButtonComponentProps } from '../ui';

const CTAButton: React.FC<CTAButtonProps> = ({ fields, rendering }) => {
  const variant = rendering.params?.variant as ButtonComponentProps['variant'];
  const buttonText = fields?.buttonText?.value as ButtonComponentProps['buttonText'];
  const iconPosition = rendering.params?.iconPosition as ButtonComponentProps['iconPosition'];
  const size = rendering.params?.size as ButtonComponentProps['size'];

  const handleClick = () => {
    console.log('CTAButton', { fields, rendering });
  };

  return (
    <ButtonComponent
      variant={variant}
      buttonText={buttonText}
      onClick={handleClick}
      iconPosition={iconPosition}
      danger={false}
      size={size}
    />
  );
};

export default withEditorChromes(CTAButton);
