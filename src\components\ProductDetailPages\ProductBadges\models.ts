import { CommerceProductBase } from '@/models';
import * as Jss from '@/models/Jss';
import { ColorProps, VariantProps } from '@pmi/catalyst-badge';

export interface ProductBadgesProps extends Jss.Rendering<CommerceProductBase> {}

export interface BadgeModel {
  id: string;
  text: string;
  color?: ColorProps;
  variant?: VariantProps;
  badgeIcon?: string;
}

export interface BadgeTextViewProps {
  icon: string;
  text: string;
}
