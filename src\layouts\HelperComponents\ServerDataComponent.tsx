import React from 'react';
import { ServerDataComponentProps } from '../models';
import { HtmlInputIds } from '../constants';

export const ServerDataComponent: React.FC<ServerDataComponentProps> = (props) => (
  <>
    <input id={HtmlInputIds.RoutePathInputId} type="hidden" value={props.routePath} />
    <input id={HtmlInputIds.LayoutInputId} type="hidden" value={JSON.stringify(props.layout)} />
    <input id={HtmlInputIds.ReduxStoreInputId} type="hidden" value={JSON.stringify(props.reduxStore)} />
    <input id={HtmlInputIds.DictionaryInputId} type="hidden" value={JSON.stringify(props.dictionary)} />
    <input id={HtmlInputIds.ProcessorInputId} type="hidden" value={JSON.stringify(props.processor)} />
  </>
);
