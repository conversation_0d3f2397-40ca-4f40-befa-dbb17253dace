@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  @font-face {
    font-family: 'Aeonik';
    src: url('https://cdn.pmi.org/dsm/fonts/Aeonik/Aeonik-Regular.woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Aeonik';
    src: url('https://cdn.pmi.org/dsm/fonts/Aeonik/Aeonik-Medium.woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Aeonik';
    src: url('https://cdn.pmi.org/dsm/fonts/Aeonik/Aeonik-Bold.woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Aeonik';
    src: url('https://cdn.pmi.org/dsm/fonts/Aeonik/Aeonik-Black.woff2');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
  }

  svg {
    display: inline;
  }

  html {
    font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif !important;
  }
}
