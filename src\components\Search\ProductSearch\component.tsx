import React, { useCallback, useMemo, useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useSitecoreContext } from '@sitecore-jss/sitecore-jss-react';
import {
  buildBreadcrumbManager,
  buildFieldSortCriterion,
  buildNumericFilter,
  buildPager,
  buildQuerySummary,
  buildRelevanceSortCriterion,
  buildResultList,
  buildResultsPerPage,
  buildSearchBox,
  buildSearchEngine,
  buildSort,
  loadAdvancedSearchQueryActions,
  SearchEngineConfiguration,
  SortOrder,
  buildSearchParameterManager,
  buildSearchStatus,
  DateRangeRequest,
} from '@coveo/headless';
import { Separator } from '@pmi/catalyst-separator';
import {
  CoveoContext,
  CoveoContextType,
  Facet,
  NumericFilter,
  FacetBreadcrumbs,
  Pager,
  QuerySummary,
  ResultsPerPage,
  SearchBox,
  Sort,
  SortOption,
  Overlay,
  SearchTips,
  Triggers,
} from '@pmi/www-shared/components';

import { searchFilterTracking, searchLoadTracking, usePageAndScreenLoad } from '@pmi/www-shared/analytics';
import { getHashFragment, createFacet } from '@pmi/www-shared/utils';

import { useCurrentStoreInformation } from '@pmi/www-shared/hooks';
import { useGetActiveMembershipsQuery } from '@pmi/www-shared/store';
import { PRODUCTSEARCH_FACETLABELS, PRODUCTSEARCH_SORTLABELS } from '@/constants';
import {
  serializeInitialAdvancedSearchParameters,
  getPageFirstResult,
  getCurrentPageNumber,
  getBreadcrumbsFacetDictionary,
  getSelectedFacets,
  deserializeSearchParameters,
  serializeSearchParameters,
} from './utils';
import { ProductSearchSkeleton } from './loading';
import { ProductSearchProps, FacetChange, FacetDictionaryValue, NumericFacetChange } from './models';
import { ProductResultList, FacetManager } from './Controls';
import { FacetId, FacetTypes, DEFAULT_SORT, MAX_PDUS } from './constants';

const Component: React.FC<ProductSearchProps> = ({ fields }) => {
  const { coveo } = fields;
  const { isLoading: membershipDataLoading, data: membership } = useGetActiveMembershipsQuery();
  const { sitecoreid: storeSitecoreId, country: storeResolvingCountry } = useCurrentStoreInformation();
  const { sitecoreContext } = useSitecoreContext();
  const { language } = sitecoreContext;
  const { t } = useTranslation();
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  const { trackScreenOrPageLoad: trackScreenLoad } = usePageAndScreenLoad(true, true);

  useEffect(() => {
    searchLoadTracking('', 0, 0, '', '', '');
  }, []);

  const fieldsToInclude = useMemo(
    () => [
      'contentsourcestitle',
      'contenttypetitle',
      'topicstitle',
      'industriestitle',
      'publicationdateformatted',
      'numberpdus',
      'authors',
      'ismemberonly',
      'haslayout',
      'pagetitle',
      'filetype',
      'z95xtemplatename',
      'z95xlanguage',
      'z95xlatestversion',
      'abstract',
      'globalz32xprice',
      'productdescription',
      'productgrouppricing',
      'productkindtitle',
      'productmemberorglobalprice',
      'productmemberprice',
      'productpriceunittype',
      'productstoretitle',
      'productthumbnailurl',
      'producttitle',
      'producttype',
      'producttypetitle',
      'productz32xkind',
      'productz32xstore',
      'z95xtemplatename',
      'productcountrypricing',
      'level3hierarchy',
      'learningLevel',
      'learningLanguage',
    ],
    [],
  );

  const coveoContext: CoveoContextType = useMemo(() => {
    const searchEngineConfig: SearchEngineConfiguration = {
      accessToken: coveo.AccessToken,
      organizationId: coveo.OrganizationId,
      search: {
        searchHub: fields.SearchHub?.value ? fields.SearchHub?.value : undefined,
      },
      analytics: {
        analyticsMode: 'legacy',
      },
    };
    const searchEngine = buildSearchEngine({ configuration: searchEngineConfig });
    return {
      searchEngine,
    };
  }, [coveo.AccessToken, coveo.OrganizationId, fields.SearchHub?.value]);

  const productTypeFacet = useMemo(
    () => createFacet(coveoContext.searchEngine, 'level3hierarchy', FacetId.ProductType),
    [coveoContext.searchEngine],
  );

  const topicsFacet = useMemo(
    () => createFacet(coveoContext.searchEngine, 'topicstitle', FacetId.Topics),
    [coveoContext.searchEngine],
  );

  const pdusFacet = useMemo(
    () =>
      buildNumericFilter(coveoContext.searchEngine, {
        options: {
          field: 'learningpdu',
          facetId: FacetId.Pdus,
        },
        initialState: {
          range: {
            start: 0,
            end: MAX_PDUS,
          },
        },
      }),
    [coveoContext.searchEngine],
  );

  const levelFacet = useMemo(
    () => createFacet(coveoContext.searchEngine, 'learningLevel', FacetId.Level),
    [coveoContext.searchEngine],
  );

  const languageFacet = useMemo(
    () => createFacet(coveoContext.searchEngine, 'learningLanguage', FacetId.Language),
    [coveoContext.searchEngine],
  );

  const searchBox = useMemo(
    () =>
      buildSearchBox(coveoContext.searchEngine, {
        options: {
          highlightOptions: {
            notMatchDelimiters: {
              open: '<strong>',
              close: '</strong>',
            },
            correctionDelimiters: {
              open: '<i>',
              close: '</i>',
            },
          },
        },
      }),
    [coveoContext.searchEngine],
  );

  const resultList = useMemo(
    () =>
      buildResultList(coveoContext.searchEngine, {
        options: {
          fieldsToInclude,
        },
      }),
    [coveoContext.searchEngine, fieldsToInclude],
  );

  const breadcrumbManager = useMemo(
    () => buildBreadcrumbManager(coveoContext.searchEngine),
    [coveoContext.searchEngine],
  );

  const querySummary = useMemo(() => buildQuerySummary(coveoContext.searchEngine), [coveoContext.searchEngine]);

  const sort = useMemo(
    () =>
      buildSort(coveoContext.searchEngine, {
        initialState: {
          criterion: buildRelevanceSortCriterion(),
        },
      }),
    [coveoContext.searchEngine],
  );

  const sortOptions: SortOption[] = useMemo(() => {
    const priceField = membership?.hasActiveMembership ? 'productmemberorglobalprice' : 'globalz32xprice';
    return [
      {
        text: t(PRODUCTSEARCH_SORTLABELS.RELEVANCE, 'Most Popular'),
        value: 'relevancy',
        criterion: buildRelevanceSortCriterion(),
      },
      {
        text: t(PRODUCTSEARCH_SORTLABELS.PRICE_ASCENDING, 'Price: Low to High'),
        value: 'priceasc',
        criterion: buildFieldSortCriterion(priceField, SortOrder.Ascending),
      },
      {
        text: t(PRODUCTSEARCH_SORTLABELS.PRICE_DESCENDING, 'Price: High to Low'),
        value: 'pricedesc',
        criterion: buildFieldSortCriterion(priceField, SortOrder.Descending),
      },
    ];
  }, [t, membership]);

  const pager = useMemo(
    () =>
      buildPager(coveoContext.searchEngine, {
        options: {
          numberOfPages: 5,
        },
      }),
    [coveoContext.searchEngine],
  );

  const resultsPerPageOptions = [10, 25, 50, 100];

  const resultsPerPage = useMemo(
    () =>
      buildResultsPerPage(coveoContext.searchEngine, {
        initialState: {
          numberOfResults: 10,
        },
      }),
    [coveoContext.searchEngine],
  );

  const facetDictionary = useMemo(
    () =>
      new Map<string, FacetDictionaryValue>([
        [
          FacetId.ProductType,
          { title: t(PRODUCTSEARCH_FACETLABELS.PRODUCT_TYPE, 'Product Type'), type: FacetTypes.Text },
        ],
        [FacetId.Topics, { title: t(PRODUCTSEARCH_FACETLABELS.TOPICS, 'Topics'), type: FacetTypes.Text }],
        [FacetId.Pdus, { title: t(PRODUCTSEARCH_FACETLABELS.PDUS, 'PDUs'), type: FacetTypes.Numeric }],
        [FacetId.Level, { title: t(PRODUCTSEARCH_FACETLABELS.LEVEL, 'Level'), type: FacetTypes.Text }],
        [FacetId.Language, { title: t(PRODUCTSEARCH_FACETLABELS.LANGUAGE, 'Language'), type: FacetTypes.Text }],
      ]),
    [t],
  );

  const sortTitleDictionary = useMemo(
    () =>
      new Map<string, string>([
        ['relevancy', 'Most Popular'],
        ['@globalz32xprice ascending', 'price ascending'],
        ['@globalz32xprice descending', 'price descending'],
        ['productmemberorglobalprice ascending', 'price ascending'],
        ['productmemberorglobalprice descending', 'price descending'],
      ]),
    [],
  );

  const isFirstRender = useRef(true);
  const prevParamsRef = useRef<Record<string, unknown>>({});

  const searchStatus = useMemo(() => buildSearchStatus(coveoContext.searchEngine), [coveoContext.searchEngine]);

  const searchParameterManager = useMemo(
    () =>
      buildSearchParameterManager(coveoContext.searchEngine, {
        initialState: { parameters: deserializeSearchParameters(facetDictionary, getHashFragment(), sortOptions) },
      }),
    [coveoContext.searchEngine, sortOptions],
  );

  const getFacetValueChanges = useCallback(
    (prevParams: Record<string, unknown>, currentParams: Record<string, unknown>): FacetChange[] => {
      if (isFirstRender.current) return [];

      const prevFacets = prevParams.f as Record<string, string[]> | undefined;
      const currentFacets = currentParams.f as Record<string, string[]> | undefined;

      if (!prevFacets && !currentFacets) return [];

      const allFacetKeys = new Set([
        ...(prevFacets ? Object.keys(prevFacets) : []),
        ...(currentFacets ? Object.keys(currentFacets) : []),
      ]);

      const changes: FacetChange[] = [];

      allFacetKeys.forEach((facetId) => {
        const prevValues = prevFacets?.[facetId] || [];
        const currentValues = currentFacets?.[facetId] || [];

        const added = currentValues.filter((val) => !prevValues.includes(val));

        if (added.length > 0) {
          changes.push({ facetId, added });
        }
      });

      return changes;
    },
    [isFirstRender],
  );

  const getNumericFacetValueChanges = useCallback(
    (prevParams: Record<string, unknown>, currentParams: Record<string, unknown>): NumericFacetChange[] => {
      if (isFirstRender.current) return [];

      const prevFacets = prevParams.nf as Record<string, DateRangeRequest[]> | undefined;
      const currentFacets = currentParams.nf as Record<string, DateRangeRequest[]> | undefined;

      if (!prevFacets && !currentFacets) return [];

      const allFacetKeys = new Set([
        ...(prevFacets ? Object.keys(prevFacets) : []),
        ...(currentFacets ? Object.keys(currentFacets) : []),
      ]);

      const changes: NumericFacetChange[] = [];

      allFacetKeys.forEach((facetId) => {
        const prevRange = prevFacets?.[facetId]?.[0] || ({} as DateRangeRequest);
        const currentRange = currentFacets?.[facetId]?.[0] || ({} as DateRangeRequest);
        if (
          (+currentRange.start > 0 || +currentRange.end < MAX_PDUS) &&
          (prevRange.start !== currentRange.start || prevRange.end !== currentRange.end)
        ) {
          changes.push({ facetId, changed: { start: +currentRange.start || 0, end: +currentRange.end || MAX_PDUS } });
        }
      });

      return changes;
    },
    [isFirstRender],
  );

  const sortChangeHasOccurred = useCallback(
    (prevParams: Record<string, unknown>, currentParams: Record<string, unknown>): boolean => {
      const prevSort = (prevParams.sortCriteria as string) || DEFAULT_SORT;
      const currentSort = (currentParams.sortCriteria as string) || DEFAULT_SORT;

      return prevSort !== currentSort && !isFirstRender.current;
    },
    [isFirstRender],
  );

  const pageChangeHasOccurred = useCallback(
    (prevParams: Record<string, unknown>, currentParams: Record<string, unknown>): boolean => {
      const prevPageFirstResult = getPageFirstResult(prevParams);
      const currentPageFirstResult = getPageFirstResult(currentParams);

      return prevPageFirstResult !== currentPageFirstResult && !isFirstRender.current;
    },
    [isFirstRender],
  );

  useEffect(() => {
    const unsubscribe = searchParameterManager.subscribe(() => {
      const currentParams = searchParameterManager.state.parameters as Record<string, unknown>;
      const prevParams = prevParamsRef.current;
      const numericFacetChanges = getNumericFacetValueChanges(prevParams, currentParams);
      if (numericFacetChanges.length > 0) {
        numericFacetChanges.forEach(({ facetId, changed }) => {
          const title = facetDictionary.get(facetId)?.title;
          searchFilterTracking(title, `${changed.start}-${changed.end}`);
        });
      }

      const hasFacets =
        Object.keys(searchParameterManager.state.parameters.f || searchParameterManager.state.parameters.nf || {})
          .length > 0;
      const prevQuery = prevParams.q || '';
      const currentQuery = currentParams.q || '';
      const hasQueryChanged = prevQuery !== currentQuery;

      const prevSort = prevParams.sortCriteria || DEFAULT_SORT;
      const currentSort = currentParams.sortCriteria || DEFAULT_SORT;
      const hasSortChanged = prevSort !== currentSort;
      const newHash = `#${serializeSearchParameters(currentParams, sortOptions)}`;
      const url = newHash.length > 1 ? newHash : window.location.pathname;
      if ((prevParams.f || prevParams.nf) && !hasFacets) {
        window.history.pushState(null, document.title, url);
      } else if (hasQueryChanged || hasSortChanged || url.length > 1) {
        if (!searchStatus.state.firstSearchExecuted) {
          window.history.replaceState(null, document.title, url);
        } else {
          window.history.pushState(null, document.title, url);
        }
      }
      prevParamsRef.current = { ...currentParams };
    });
    const onHashChange = () => {
      const parameters = deserializeSearchParameters(
        facetDictionary,
        typeof window !== 'undefined' ? window.location.hash : '',
        sortOptions,
      );
      searchParameterManager.synchronize(parameters);
    };

    window.addEventListener('hashchange', onHashChange);

    return () => {
      unsubscribe();
      window.removeEventListener('hashchange', onHashChange);
    };
  }, [
    pageChangeHasOccurred,
    sortChangeHasOccurred,
    getFacetValueChanges,
    getNumericFacetValueChanges,
    searchParameterManager,
    searchStatus,
    sortOptions,
    facetDictionary,
    resultsPerPage,
    coveoContext.searchEngine,
  ]);

  useEffect(() => {
    const action = loadAdvancedSearchQueryActions(coveoContext.searchEngine).registerAdvancedSearchQueries({
      aq: serializeInitialAdvancedSearchParameters(fields.Templates, storeSitecoreId),
      cq: `(@z95xlanguage==${language}) (@z95xlatestversion==1) (@source=="${coveo.Source}")`,
    });
    coveoContext.searchEngine.dispatch(action);
    coveoContext.searchEngine.executeFirstSearch();
  }, [coveoContext.searchEngine, language, coveo.Source, fields.Templates, storeSitecoreId]);

  const [isFilterOverlayOpen, setIsFilterOverlayOpen] = useState(false);

  const renderFacets = useCallback(
    () => (
      <FacetManager controller={coveoContext.searchEngine}>
        <Facet title={facetDictionary.get(FacetId.ProductType)?.title} controller={productTypeFacet} />
        <Facet title={facetDictionary.get(FacetId.Topics)?.title} controller={topicsFacet} />
        <NumericFilter
          title={facetDictionary.get(FacetId.Pdus)?.title}
          controller={pdusFacet}
          range={{ start: 0, end: MAX_PDUS }}
          minStepsBetweenThumbs={1}
        />
        <Facet title={facetDictionary.get(FacetId.Level)?.title} controller={levelFacet} />
        <Facet title={facetDictionary.get(FacetId.Language)?.title} controller={languageFacet} />
      </FacetManager>
    ),
    [coveoContext.searchEngine, facetDictionary, productTypeFacet, topicsFacet, pdusFacet, levelFacet, languageFacet],
  );

  const handleCloseOverlay = useCallback(() => {
    setIsFilterOverlayOpen(false);
  }, []);

  const handleFilter = useCallback(() => {
    setIsFilterOverlayOpen(true);
  }, []);

  const [isQuerying, setIsQuerying] = useState(true);
  const [hasResults, setHasResults] = useState(false);

  useEffect(() => {
    const unsubscribe = querySummary.subscribe(() => {
      setIsQuerying(querySummary.state.isLoading);
      setHasResults(querySummary.state.hasResults);
    });
    return () => unsubscribe();
  }, [querySummary]);

  useEffect(() => {
    const unsubscribe = searchStatus.subscribe(() => {
      if (searchStatus.state.firstSearchExecuted) {
        setIsInitialLoading(false);
      }
    });
    return () => unsubscribe();
  }, [searchStatus]);

  const runSearchLoadTracking = useCallback(
    (screenLoad: boolean = false) => {
      const selectedFacets = getSelectedFacets(
        [productTypeFacet, topicsFacet, pdusFacet, levelFacet, languageFacet],
        facetDictionary,
      );
      const currentParams = searchParameterManager.state.parameters as Record<string, unknown>;
      const currentPageFirstResult = getPageFirstResult(currentParams);
      const currentPageNumber = getCurrentPageNumber(currentPageFirstResult, resultsPerPage);

      searchLoadTracking(
        selectedFacets,
        currentPageNumber,
        querySummary.state.total,
        sortTitleDictionary.get(sort.state.sortCriteria),
        querySummary.state.query,
        'store',
      );
      if (screenLoad) trackScreenLoad();
    },
    [
      querySummary.state.query,
      querySummary.state.total,
      resultsPerPage,
      searchParameterManager.state.parameters,
      sort.state.sortCriteria,
    ],
  );

  useEffect(() => {
    const unsubscribe = searchStatus.subscribe(() => {
      const { firstSearchExecuted } = searchStatus.state;

      if (firstSearchExecuted && !isQuerying && isFirstRender.current) {
        runSearchLoadTracking();
        isFirstRender.current = false;
      }
    });
    return () => unsubscribe();
  }, [searchStatus, isQuerying, runSearchLoadTracking]);

  if (isInitialLoading || membershipDataLoading) {
    return <ProductSearchSkeleton />;
  }

  const adobeRegionSort = { adoberegion: 'coveo-sort-section' };

  return (
    <CoveoContext.Provider value={coveoContext}>
      <div className="grid grid-cols-12 max-w-screen-xl mx-auto px-6 lg:px-4 pb-20 mt-14">
        <div className="col-span-12">
          <div className="mb-6">
            <SearchBox
              controller={searchBox}
              engine={coveoContext.searchEngine}
              onSubmit={() => runSearchLoadTracking(true)}
            />
          </div>
          <FacetBreadcrumbs
            controller={breadcrumbManager}
            facetDictionary={getBreadcrumbsFacetDictionary(facetDictionary)}
            onFilter={handleFilter}
          />
          <Separator className="mt-6" />
          <div
            className="flex flex-col sm:flex-row sm:justify-between items-center gap-4 mt-4 mb-6 text-body-sm"
            {...adobeRegionSort}
          >
            <QuerySummary controller={querySummary} />
            {hasResults && <Sort controller={sort} options={sortOptions} />}
          </div>
        </div>
        {!hasResults && (
          <div className="col-span-12 mb-6">
            <SearchTips />
          </div>
        )}
        <div className="col-span-12 lg:flex lg:gap-6">
          <div className="mobile:hidden lg:basis-[336px] flex flex-col shrink-0 gap-3">{renderFacets()}</div>
          <div className="lg:basis-[792px] flex-grow xl:-mr-6">
            <div className="flex flex-col gap-[--scale-32] overflow-wrap-anywhere">
              <Triggers searchEngine={coveoContext.searchEngine} />
              <ProductResultList
                controller={resultList}
                storeResolvingCountry={storeResolvingCountry}
                membership={membership}
              />
              {hasResults && (
                <div className="flex flex-col sm:flex-row justify-between items-center mt-4 gap-4 font-display">
                  <div>
                    <Pager controller={pager} />
                  </div>
                  <div className="flex justify-center">
                    <ResultsPerPage controller={resultsPerPage} options={resultsPerPageOptions} />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      {isFilterOverlayOpen && (
        <Overlay controller={breadcrumbManager} onClose={handleCloseOverlay}>
          {renderFacets()}
        </Overlay>
      )}
    </CoveoContext.Provider>
  );
};

export default Component;
