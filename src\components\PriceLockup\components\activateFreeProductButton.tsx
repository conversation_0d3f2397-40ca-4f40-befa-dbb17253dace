import React, { useCallback, useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useAtom, useSetAtom } from 'jotai';
import { ButtonComponent, ConfirmationDialog, LoadingOverlay } from '@/components/ui';
import { PRODUCTDETAILPAGES_BUTTONLABELS } from '@/constants';
import {
  useFreeProductActivationTracking,
  useLoadingDebounce,
  UserFulfillmentResult,
  useSignalRService,
  useSpxHeadlessSettings,
} from '@/hooks';
import { isFetchBaseQueryErrorType, useActivateFreeProductMutation } from '@/store';
import { getProductType, navigateToLoginWithRedirectToCurrentPage, navigateToUrl } from '@/utils';
import { launchFreeProductActivationSkuAtom, showFreeProductActivationErrorModalAtom } from '@/atoms';
import { linkClickTracking, linkClickTrackingRaw } from '@pmi/www-shared/analytics';
import { useGetCoursesQuery } from '@pmi/www-shared/store';
import { ActivateFreeProductButtonProps } from '../models';
import { usePriceLookupContext } from '../context';

export const ActivateFreeProductButton: React.FC<ActivateFreeProductButtonProps> = ({ sku, variant, size, danger }) => {
  const { t } = useTranslation();
  const signalRConnection = useSignalRService();
  const [activateFreeProductMutation, { isLoading, isError, error }] = useActivateFreeProductMutation();
  const {
    trackFreeProductActivationSuccessEvent,
    trackLaunchFreeCourseClickEvent,
    trackFreeProductGetAccessButtonClickEvent,
  } = useFreeProductActivationTracking();
  const [loading, setLoading] = useLoadingDebounce(false, 500);
  const buttonContainerRef = useRef<HTMLDivElement>(null);
  const signalRReconnectionAttemptRef = useRef(0);
  const [showConfirm, setShowConfirm] = useState(false);
  const [courseId, setCourseId] = useState(null);
  const [launchFreeProductActivationSku, setLaunchFreeProductActivationSku] = useAtom(
    launchFreeProductActivationSkuAtom,
  );
  const setShowFreeProductActivationErrorModal = useSetAtom(showFreeProductActivationErrorModalAtom);
  const { product, fields, isAuthenticated, isInCart, isLoading: isLoadingCart } = usePriceLookupContext();
  const productType = getProductType(product);
  const settings = useSpxHeadlessSettings();
  const startTime = useRef(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const defaultTimeout = 15;
  const defaultSignalRWaitExceedRedirectUrl = 'https://my.pmi.org/account/orders';
  const defaultConnectionRetryCount = 0;
  const defaultConnectionRetryTimeout = 1;
  const signalRConnectionRetryCount =
    settings?.SignalRConnectionRetryCount != null && settings?.SignalRConnectionRetryCount !== ''
      ? Number(settings?.SignalRConnectionRetryCount)
      : defaultConnectionRetryCount;
  const signalRConnectionRetrytimeout =
    (settings?.SignalRConnectionRetryTimeoutInSeconds != null && settings?.SignalRConnectionRetryTimeoutInSeconds !== ''
      ? Number(settings?.SignalRConnectionRetryTimeoutInSeconds)
      : defaultConnectionRetryTimeout) * 1000;
  const { refetch: coursesRefetch } = useGetCoursesQuery(null, { skip: !isAuthenticated });

  const adobeRegion = 'pdp-activation';

  const buttonText = t(PRODUCTDETAILPAGES_BUTTONLABELS.GETACCESSBUTTON, 'Get Access');
  const disabledCTA = product?.IsDeleted?.value === true || isInCart || isLoadingCart;

  const clearTimeoutTimer = () => {
    if (timeoutRef?.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  const setTimeoutTimer = () => {
    timeoutRef.current = setTimeout(
      () => {
        navigateToUrl(settings?.SignalRWaitExceedRedirectUrl ?? defaultSignalRWaitExceedRedirectUrl);
      },
      (settings?.SignalRResponseWaitTimeoutInSeconds ?? defaultTimeout) * 1000,
    );
  };

  const startSignalRConnection = () => {
    signalRConnection.signalRConnection
      .start()
      .then(() => {
        startTime.current = new Date().getTime();
      })
      .catch((ex) => {
        console.log('SignalR connection start block error:', ex);
        clearTimeoutTimer();
        if (ex?.message.toLowerCase().includes("status code '401'")) {
          navigateToLoginWithRedirectToCurrentPage();
          return;
        }
        if (signalRReconnectionAttemptRef?.current < signalRConnectionRetryCount) {
          signalRReconnectionAttemptRef.current += 1;
          console.log(`Attempt ${signalRReconnectionAttemptRef.current}. Trying to reconnect to SignalR...`);
          setTimeoutTimer();
          setTimeout(() => startSignalRConnection(), signalRConnectionRetrytimeout);
          return;
        }
        signalRReconnectionAttemptRef.current = 0;
        setLoading(false);
        setShowFreeProductActivationErrorModal(true);
      });
  };

  const activateFreeProduct = async () => {
    setLoading(true);
    signalRConnection.signalRConnection.on('userFulfillmentResult', (message: string) => {
      const elapsedTimeInSeconds = (new Date().getTime() - startTime.current) / 1000;
      setLoading(false);
      setShowConfirm(true);
      const response: UserFulfillmentResult = JSON.parse(message);
      setCourseId(response.registration_id);
      signalRConnection.signalRConnection.stop();
      clearTimeoutTimer();
      trackFreeProductActivationSuccessEvent(productType, sku, elapsedTimeInSeconds.toString());
    });

    setTimeoutTimer();
    startSignalRConnection();

    try {
      await activateFreeProductMutation({ sku, qty: 1 });
    } catch (ex) {
      signalRConnection.signalRConnection.stop();
      clearTimeoutTimer();
      setLoading(false);
      setShowFreeProductActivationErrorModal(true);
      console.error('SignalR connection stopped because of error: ', ex);
    }
  };

  const handleClick = () => {
    trackFreeProductGetAccessButtonClickEvent();
    linkClickTracking(buttonText, buttonContainerRef.current, '');
    if (!isAuthenticated) {
      setLaunchFreeProductActivationSku(sku);
      navigateToLoginWithRedirectToCurrentPage();
      return;
    }
    activateFreeProduct();
  };

  useEffect(() => {
    return () => {
      clearTimeoutTimer();
    };
  }, []);

  useEffect(() => {
    if (isAuthenticated && launchFreeProductActivationSku === sku) {
      setLaunchFreeProductActivationSku(null);
      activateFreeProduct();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, launchFreeProductActivationSku, sku, setLaunchFreeProductActivationSku]);

  useEffect(() => {
    if (!isLoading && isError) {
      setLoading(false);
      signalRConnection.signalRConnection.stop();
      if (isFetchBaseQueryErrorType(error) && error.status === 401) {
        console.info('error 401', error);
        navigateToLoginWithRedirectToCurrentPage();
      }
      clearTimeoutTimer();
      setShowFreeProductActivationErrorModal(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isError, isLoading]);

  const confirmHandler = useCallback(() => {
    const targetUrl = `${settings?.LMSUrl}/course?id=${courseId}`;
    trackLaunchFreeCourseClickEvent();
    linkClickTrackingRaw(fields?.confirmButtonText?.value as string, adobeRegion, targetUrl);
    navigateToUrl(targetUrl);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [courseId, settings?.LMSUrl]);

  const closeCallback = useCallback(
    (open: boolean) => {
      if (!open) {
        coursesRefetch();
      }
    },
    [coursesRefetch],
  );

  const showButton = !showConfirm && !loading;

  return (
    <>
      <div ref={buttonContainerRef}>
        {showButton && (
          <ButtonComponent
            classNames="w-full lg:w-max"
            disabled={disabledCTA}
            variant={variant}
            buttonText={buttonText}
            onClick={handleClick}
            danger={danger}
            size={size}
            mobileStickyFooter
          />
        )}
      </div>
      <LoadingOverlay
        open={loading}
        description={t('ProductDetailPages.OverlayLabels.ProductActivation', 'Unlocking new insights.')}
      />
      {showConfirm && (
        <ConfirmationDialog
          {...fields}
          productName={product?.Name?.value as string}
          onConfirm={confirmHandler}
          onCloseCallback={closeCallback}
          adobeRegion={adobeRegion}
        />
      )}
    </>
  );
};
