import rspack from '@rspack/core';

export default () =>
  new rspack.DefinePlugin({
    'SERVER_VARS.SITECORE_URL': JSON.stringify(process.env.SITECORE_URL),
    'SERVER_VARS.SITECORE_API_KEY': JSON.stringify(process.env.SITECORE_API_KEY),
    'SERVER_VARS.SITECORE_SITE_NAME': JSON.stringify(process.env.SITECORE_SITE_NAME),
    'SERVER_VARS.SITECORE_CONFIG_NAME': JSON.stringify(process.env.SITECORE_CONFIG_NAME),
    'SERVER_VARS.API_BASE_URL': JSON.stringify(process.env.API_BASE_URL),
    'SERVER_VARS.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
    'SERVER_VARS.PMI_GRAPHQL_URL': JSON.stringify(process.env.PMI_GRAPHQL_URL),
  });
