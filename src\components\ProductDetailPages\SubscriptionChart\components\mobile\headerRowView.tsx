import React from 'react';
import { ChevronRightIcon } from '@pmi/catalyst-icons';
import { cn } from '@pmi/catalyst-utils';
import { HeaderRow, RowViewProps } from '../models';

export const HeaderRowView: React.FC<RowViewProps> = ({ data, leftColumnCondition, rightColumnCondition }) => {
  const headerRows = JSON.parse(data as string);

  if (!headerRows) {
    return null;
  }

  const drawBorder = cn(
    { 'first:border-x-2': leftColumnCondition, 'last:border-x-2': rightColumnCondition },
    { 'first:border-t-2': leftColumnCondition, 'last:border-t-2': rightColumnCondition },
    { 'first:rounded-t-3xl': leftColumnCondition, 'last:rounded-t-3xl': rightColumnCondition },
  );

  return (
    <>
      {headerRows.map((headerRow: HeaderRow) => (
        <th
          className={`p-6 align-top text-center font-display font-normal border-[--colors-off-black-800] ${drawBorder}`}
          key={headerRow?.title}
        >
          <h5 className="font-medium text-header-2xs">{headerRow?.title}</h5>
          <div className="text-body-sm py-2">{headerRow?.price}</div>
          <div className="text-body-xs text-[--text-secondary] pb-2">{headerRow?.description}</div>
          {headerRow?.actionLink?.url && (
            <div className="font-medium text-body-xs pb-4">
              <a className="hover:underline" href={headerRow?.actionLink?.url}>
                {headerRow?.actionLink?.text}
                <ChevronRightIcon size="xs" />
              </a>
            </div>
          )}
        </th>
      ))}
    </>
  );
};
