import React from 'react';
import { withEditorChromes } from '@sitecore-jss/sitecore-jss-react';
import { Badge, ColorProps, VariantProps } from '@pmi/catalyst-badge';
import { withProductFields } from '@/hocs';
import { IconByName } from '@/components/common';
import { ProductBadgesProps, BadgeModel } from './models';
import BadgeTextView from './badgeTextView';

const Component: React.FC<ProductBadgesProps> = ({ fields }) => {
  const productTypeBadges: BadgeModel[] =
    fields.level_3_hierarchy?.map((item) => ({
      id: item?.id,
      text: item?.name,
      color: 'off-black',
      variant: 'outline',
    })) || [];

  const promoBadges: BadgeModel[] =
    fields.PromoBadges?.map((item) => ({
      id: item?.id,
      text: item?.fields?.Value?.value as string,
      color: (item?.fields?.Color?.value as ColorProps) || 'off-black',
      variant: (item?.fields?.Variant?.value as VariantProps) || 'outline',
      badgeIcon: item?.fields?.BadgeIcon?.value,
    })) || [];

  const badges = [...productTypeBadges, ...promoBadges];

  if (badges.length === 0) return null;

  return (
    <div className="flex flex-wrap gap-2 mb-4">
      {badges?.map((item) => {
        return (
          <Badge key={item?.id} color={item.color} variant={item.variant}>
            <IconByName name={item.badgeIcon} size="xs" color="original" className="mr-1" />
            <BadgeTextView icon={item.badgeIcon} text={item.text} />
          </Badge>
        );
      })}
    </div>
  );
};

export default withEditorChromes(withProductFields(Component));
