import { MissingDataSource } from '@/components/common';
import { PRODUCTDETAILPAGES_FIELDLABELS, PRODUCTDETAILPAGES_PHRASES } from '@/constants';
import { withProductFields } from '@/hocs';
import { useIsExperienceEditor } from '@/hooks';
import { ElearningItem } from '@/models';
import { toFixedIfNecessary } from '@/utils';
import { Separator } from '@pmi/catalyst-separator';
import { Text, withEditorChromes } from '@sitecore-jss/sitecore-jss-react';
import React, { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { CourseDetailProps } from './models';

const Detail = ({ label, value }: { label: string | ReactNode; value: string | number | ReactNode }) => {
  return (
    <div className="flex flex-col gap-[--scale-4]">
      <p className="text-body-sm lg:text-body-md font-bold">{label}</p>
      <p className="text-body-sm lg:text-body-md font-normal">{value}</p>
    </div>
  );
};

const Component: React.FC<CourseDetailProps> = ({ fields }) => {
  const { t } = useTranslation();
  const { pdus, length_of_course: courseDuration, AssociatedCertifications: associatedCertifications } = fields;
  const { OverviewLanguagesEx: languages = [] } = (fields as ElearningItem) || {};

  const isExperienceEditor = useIsExperienceEditor();
  const hasPdus = !!pdus?.value;
  const hasCourseDuration = !!courseDuration?.value;
  const hasMultipleLanguages = languages?.length > 1;
  const hasAssociatedCertifications = !!associatedCertifications?.value;

  if (!hasPdus && !hasCourseDuration && !hasMultipleLanguages && !hasAssociatedCertifications)
    return isExperienceEditor ? <MissingDataSource /> : null;

  const { PDUS, COURSEDURATION, ASSOCIATEDCERTIFICATIONS } = PRODUCTDETAILPAGES_FIELDLABELS.DIGITALPRODUCTBASE;

  const pdusLabel = t(PDUS, 'PDUs');
  const courseDurationLabel = t(COURSEDURATION, 'Course Duration');
  const associatedCertificationsLabel = t(ASSOCIATEDCERTIFICATIONS, 'Associated Certifications');
  const hoursPhrase =
    +courseDuration.value === 1
      ? t(PRODUCTDETAILPAGES_PHRASES.HOUR, 'hour')
      : t(PRODUCTDETAILPAGES_PHRASES.HOURS, 'hours');

  return (
    <div className="w-full">
      <Separator className="col-span-4 lg:col-span-6 my-[--scale-24] bg-[--fill-off-black-darkest]" />
      <div className="w-full my-[--scale-24]">
        <div className="max-w-screen-xl mx-auto">
          <div className="flex md:flex-row flex-wrap gap-4 justify-between items-start md:items-center">
            {hasPdus && (
              <div className="flex flex-col md:pr-4">
                <span className="text-[#2d2a3e] text-lg font-bold">{pdusLabel}</span>
                <span className="text-[#2d2a3e] text-lg font-normal mt-1">{toFixedIfNecessary(+pdus.value, 2)}</span>
              </div>
            )}

            {hasPdus && hasCourseDuration && <Separator className="hidden md:block h-16" orientation="vertical" />}

            {hasCourseDuration && (
              <div className="flex flex-col md:px-4">
                <span className="text-[#2d2a3e] text-lg font-bold">{courseDurationLabel}</span>
                <span className="text-[#2d2a3e] text-lg font-normal mt-1">{`${courseDuration.value} ${hoursPhrase}`}</span>
              </div>
            )}

            {hasCourseDuration && hasAssociatedCertifications && (
              <Separator className="hidden md:block h-16" orientation="vertical" />
            )}

            {hasAssociatedCertifications && (
              <div className="flex flex-col md:px-4">
                <span className="text-[#2d2a3e] text-lg font-bold">{associatedCertificationsLabel}</span>
                <span className="text-[#2d2a3e] text-lg font-normal mt-1">
                  <Text field={associatedCertifications} />
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default withEditorChromes(withProductFields(Component));
