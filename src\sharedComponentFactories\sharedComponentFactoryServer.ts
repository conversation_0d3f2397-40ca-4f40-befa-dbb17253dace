import { pipelineComponentName } from '@pmi/shared-component-factory/serverRuntime';
import { mainHandlerAsync } from '@/handlers/serverside';
import sharedComponentFactory from './sharedComponentFactory';

export default (componentName: string, exportName?: string) => {
  if (componentName === pipelineComponentName) {
    return mainHandlerAsync;
  }
  return sharedComponentFactory(componentName);
};
