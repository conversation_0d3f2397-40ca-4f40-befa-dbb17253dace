import React from 'react';
import { Button } from '@pmi/catalyst-button';
import { ExternalLinkIcon } from '@pmi/catalyst-icons';
import { useRouteFields } from '@/hooks';
import { ChapterMembershipProductItem } from '@/models';
import { ChapterActionLinkProps } from './models';

export const PdpChapterActionLink: React.FC<ChapterActionLinkProps> = ({ fields }) => {
  const product = useRouteFields<ChapterMembershipProductItem>();
  const url = product?.url?.value?.toString();
  if (!url) return null;

  const parsedUrl = url?.startsWith('http://') || url?.startsWith('https://') ? url : `http://${url}`;

  return (
    <div className="flex justify-center lg:justify-start theme-pmi-dark">
      <Button asChild variant="outline" size="sm">
        <a href={parsedUrl} className="text-body-xs font-medium">
          {fields?.LinkText?.value || 'Visit Chapter Site'}
          <ExternalLinkIcon size="xs" className="transition group-hover:text-[--text-primary]" />
        </a>
      </Button>
    </div>
  );
};
