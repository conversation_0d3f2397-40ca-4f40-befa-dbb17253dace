import * as signalR from '@microsoft/signalr';
import { useGetUserQuery } from '@pmi/www-shared/store';
import { useSpxHeadlessSettings } from '../Sitecore';

const useSignalRService = () => {
  const { data: userData } = useGetUserQuery();
  const settings = useSpxHeadlessSettings();

  const signalRConnection = new signalR.HubConnectionBuilder()
    .withUrl(`${settings.SignalRNegotiateUrl}?userId=${userData?.personId.toString()}`)
    .build();

  return {
    signalRConnection,
  };
};

export default useSignalRService;
