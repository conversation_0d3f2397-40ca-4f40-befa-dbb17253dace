/* eslint-disable react/no-array-index-key */
import React from 'react';
import { CircleCheckFilledIcon } from '@pmi/catalyst-icons';
import { cn } from '@pmi/catalyst-utils';
import { BodyRow, RowViewProps, ColumntValueProps } from '../models';

export const BodyRowView: React.FC<RowViewProps> = ({ data, leftColumnCondition, rightColumnCondition }) => {
  const bodyRows = JSON.parse(data as string);

  if (!bodyRows) {
    return null;
  }

  const drawRowsBorder = cn({
    '[tr:not(:last-child)_&:first-child]:border-x-2': leftColumnCondition,
    '[tr:not(:last-child)_&:last-child]:border-x-2': rightColumnCondition,
  });
  const drawLastRowBorder = cn(
    {
      '[tr:last-child_td:first-child_&]:border-x-2': leftColumnCondition,
      '[tr:last-child_td:last-child_&]:border-x-2': rightColumnCondition,
    },
    {
      '[tr:last-child_td:first-child_&]:border-b-2': leftColumnCondition,
      '[tr:last-child_td:last-child_&]:border-b-2': rightColumnCondition,
    },
    {
      '[tr:last-child_td:first-child_&]:rounded-b-3xl': leftColumnCondition,
      '[tr:last-child_td:last-child_&]:rounded-b-3xl': rightColumnCondition,
    },
  );

  return (
    <>
      {bodyRows.map((bodyRow: BodyRow, bodyRowIndex: number) => (
        <tr key={`m-tr-${bodyRowIndex}`} className="odd:bg-[--surface-secondary]">
          {bodyRow.columnValues.map((columnValue: ColumntValueProps, columnIndex: number) => (
            <td
              key={`m-td-${bodyRow?.title}-${columnIndex}`}
              className={`p-0 align-top text-center text-header-xs border-[--colors-off-black-800] ${drawRowsBorder}`}
            >
              <div className={`min-h-32 py-6 border-[--colors-off-black-800] ${drawLastRowBorder}`}>
                <div className="text-body-md">{bodyRow.title}</div>
                {bodyRow.description && (
                  <div className="text-body-sm text-[--text-secondary]">{bodyRow.description}</div>
                )}
                <div className="flex justify-center w-full font-medium pt-5">
                  {typeof columnValue.value === 'boolean'
                    ? columnValue.value && <CircleCheckFilledIcon size="lg" />
                    : columnValue.value}
                </div>
              </div>
            </td>
          ))}
        </tr>
      ))}
    </>
  );
};
